-- 创建测试租户数据
-- 用于验证租户管理统计功能

-- 插入测试租户数据
INSERT INTO tenants (
    id,
    name,
    display_name,
    description,
    type,
    category,
    contact_name,
    contact_email,
    contact_phone,
    address,
    website,
    license_number,
    tax_id,
    legal_person,
    status,
    is_active,
    is_verified,
    settings,
    metadata,
    max_users,
    max_projects,
    max_applications,
    storage_limit
) VALUES 
-- 活跃租户
('tenant_001', '北京协和医院', '北京协和医院', '国内顶级三甲医院', 'hospital_tertiary', '综合医院', '张主任', '<EMAIL>', '010-12345678', '北京市东城区', 'https://www.xiehe.com', 'L001', 'T001', '张院长', 'active', true, true, '{"theme": "light"}', '{"region": "beijing"}', 1000, 10, 50, **********),

('tenant_002', '上海华山医院', '上海华山医院', '知名三甲医院', 'hospital_tertiary', '综合医院', '李主任', '<EMAIL>', '021-87654321', '上海市静安区', 'https://www.huashan.com', 'L002', 'T002', '李院长', 'active', true, true, '{"theme": "dark"}', '{"region": "shanghai"}', 800, 8, 40, **********),

('tenant_003', '广州中山医院', '广州中山医院', '华南地区知名医院', 'hospital_secondary', '综合医院', '王主任', '<EMAIL>', '020-11111111', '广州市天河区', 'https://www.zhongshan.com', 'L003', 'T003', '王院长', 'active', true, true, '{"theme": "light"}', '{"region": "guangzhou"}', 600, 6, 30, **********),

-- 待审核租户
('tenant_004', '深圳人民医院', '深圳人民医院', '深圳市重点医院', 'hospital_secondary', '综合医院', '刘主任', '<EMAIL>', '0755-22222222', '深圳市福田区', 'https://www.szrm.com', 'L004', 'T004', '刘院长', 'pending', false, false, '{"theme": "light"}', '{"region": "shenzhen"}', 500, 5, 25, 536870912),

('tenant_005', '杭州西湖医院', '杭州西湖医院', '杭州市专科医院', 'hospital_specialized', '专科医院', '陈主任', '<EMAIL>', '0571-33333333', '杭州市西湖区', 'https://www.xihu.com', 'L005', 'T005', '陈院长', 'pending', false, false, '{"theme": "dark"}', '{"region": "hangzhou"}', 300, 3, 15, 268435456),

-- 暂停租户
('tenant_006', '成都华西医院', '成都华西医院', '西南地区知名医院', 'hospital_tertiary', '综合医院', '赵主任', '<EMAIL>', '028-44444444', '成都市武侯区', 'https://www.huaxi.com', 'L006', 'T006', '赵院长', 'suspended', false, true, '{"theme": "light"}', '{"region": "chengdu"}', 700, 7, 35, **********),

-- 诊所类型
('tenant_007', '北京爱康诊所', '北京爱康诊所', '连锁诊所品牌', 'clinic', '诊所', '孙医生', '<EMAIL>', '010-55555555', '北京市朝阳区', 'https://www.aikang.com', 'L007', 'T007', '孙医生', 'active', true, true, '{"theme": "light"}', '{"region": "beijing"}', 50, 2, 5, 134217728),

-- 卫生院类型
('tenant_008', '上海浦东卫生院', '上海浦东卫生院', '社区卫生服务中心', 'health_center', '卫生院', '周医生', '<EMAIL>', '021-66666666', '上海市浦东新区', 'https://www.pudong.com', 'L008', 'T008', '周医生', 'active', true, true, '{"theme": "dark"}', '{"region": "shanghai"}', 100, 3, 10, 268435456),

-- 医疗集团
('tenant_009', '华润医疗集团', '华润医疗集团', '大型医疗集团', 'medical_group', '医疗集团', '吴总监', '<EMAIL>', '010-77777777', '北京市海淀区', 'https://www.huarun.com', 'L009', 'T009', '吴总', 'active', true, true, '{"theme": "light"}', '{"region": "beijing"}', 2000, 20, 100, **********),

-- 其他类型
('tenant_010', '互联网医院', '互联网医院', '在线医疗服务平台', 'other', '互联网医院', '马经理', '<EMAIL>', '400-88888888', '北京市中关村', 'https://www.online.com', 'L010', 'T010', '马总', 'pending', false, false, '{"theme": "dark"}', '{"region": "online"}', 10000, 100, 500, **********0)

ON CONFLICT (id) DO NOTHING;

-- 创建租户组织关联数据（如果需要的话）
-- 注意：这里需要根据实际的组织ID来创建关联
-- 可以先查询现有的组织ID，然后创建关联

-- 查询现有组织（示例）
-- SELECT id, name FROM organizations LIMIT 5;

-- 假设有一个组织ID为 'org_example'，创建关联
-- INSERT INTO tenant_organizations (
--     id,
--     tenant_id,
--     org_id,
--     role,
--     created_at,
--     updated_at
-- ) VALUES 
-- ('to_001', 'tenant_001', 'org_example', 'member', NOW(), NOW()),
-- ('to_002', 'tenant_002', 'org_example', 'member', NOW(), NOW()),
-- ('to_003', 'tenant_003', 'org_example', 'member', NOW(), NOW()),
-- ('to_004', 'tenant_004', 'org_example', 'member', NOW(), NOW()),
-- ('to_005', 'tenant_005', 'org_example', 'member', NOW(), NOW()),
-- ('to_006', 'tenant_006', 'org_example', 'member', NOW(), NOW()),
-- ('to_007', 'tenant_007', 'org_example', 'member', NOW(), NOW()),
-- ('to_008', 'tenant_008', 'org_example', 'member', NOW(), NOW()),
-- ('to_009', 'tenant_009', 'org_example', 'member', NOW(), NOW()),
-- ('to_010', 'tenant_010', 'org_example', 'member', NOW(), NOW())
-- ON CONFLICT (id) DO NOTHING;

-- 创建测试配额分配数据（用于验证删除逻辑）
-- 注意：需要先有项目ID，这里使用示例项目ID
-- 实际使用时请替换为真实的项目ID

-- 为tenant_001创建配额分配（阻止删除）
INSERT INTO quota_allocations (
    id,
    project_id,
    resource_type,
    resource_id,
    quota_type,
    limit,
    used,
    period,
    lifecycle_period,
    warning_threshold,
    status,
    tenant_id,
    created_at,
    updated_at
) VALUES
('qa_001', 'your_project_id_here', 'TENANT', 'tenant_001', 'API_CALLS', 10000, 1500, 'MONTHLY', 'ONE_YEAR', 80, 'NORMAL', 'tenant_001', NOW(), NOW()),
('qa_002', 'your_project_id_here', 'TENANT', 'tenant_001', 'STORAGE', **********, 268435456, 'MONTHLY', 'ONE_YEAR', 80, 'NORMAL', 'tenant_001', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 为tenant_002创建旧配额数据（阻止删除）
INSERT INTO tenant_quotas (
    id,
    tenant_id,
    quota_type,
    limit,
    used,
    period,
    reset_at,
    created_at,
    updated_at
) VALUES
('tq_001', 'tenant_002', 'API_CALLS', 5000, 800, 'MONTHLY', NOW() + INTERVAL '1 month', NOW(), NOW()),
('tq_002', 'tenant_002', 'USERS', 100, 25, 'MONTHLY', NOW() + INTERVAL '1 month', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 验证数据插入
SELECT
    status,
    COUNT(*) as count
FROM tenants
GROUP BY status
ORDER BY status;

-- 显示所有测试租户
SELECT
    id,
    name,
    type,
    status,
    is_active,
    is_verified,
    created_at
FROM tenants
ORDER BY created_at DESC;

-- 显示配额分配关联
SELECT
    t.id as tenant_id,
    t.name as tenant_name,
    COUNT(qa.id) as quota_allocations,
    COUNT(tq.id) as tenant_quotas
FROM tenants t
LEFT JOIN quota_allocations qa ON t.id = qa.tenant_id
LEFT JOIN tenant_quotas tq ON t.id = tq.tenant_id
WHERE t.id IN ('tenant_001', 'tenant_002', 'tenant_003', 'tenant_004', 'tenant_005')
GROUP BY t.id, t.name
ORDER BY t.id;

-- 测试删除验证的说明
-- 1. tenant_001 有新配额分配，删除时应该被阻止
-- 2. tenant_002 有旧配额数据，删除时应该被阻止
-- 3. tenant_003 到 tenant_010 没有配额分配，可以正常删除
