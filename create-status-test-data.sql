-- 创建不同状态的测试数据
-- 用于验证配额分配创建时的状态验证逻辑

-- 更新部分租户为不同状态（用于测试状态验证）
UPDATE tenants SET 
    status = 'pending',
    is_active = false
WHERE id = 'tenant_004';

UPDATE tenants SET 
    status = 'suspended',
    is_active = false
WHERE id = 'tenant_006';

UPDATE tenants SET 
    status = 'active',
    is_active = false  -- 状态为active但未激活
WHERE id = 'tenant_005';

-- 创建测试应用数据（不同状态）
INSERT INTO applications (
    id,
    name,
    description,
    type,
    status,
    project_id,
    created_at,
    updated_at
) VALUES 
-- 活跃应用
('app_001', '测试应用1', '活跃状态的测试应用', 'WEB', 'ACTIVE', 'your_project_id_here', NOW(), NOW()),
('app_002', '测试应用2', '活跃状态的测试应用', 'MOBILE', 'ACTIVE', 'your_project_id_here', NOW(), NOW()),

-- 非活跃应用
('app_003', '测试应用3', '非活跃状态的测试应用', 'WEB', 'INACTIVE', 'your_project_id_here', NOW(), NOW()),
('app_004', '测试应用4', '暂停状态的测试应用', 'API', 'SUSPENDED', 'your_project_id_here', NOW(), NOW()),
('app_005', '测试应用5', '草稿状态的测试应用', 'WEB', 'DRAFT', 'your_project_id_here', NOW(), NOW())

ON CONFLICT (id) DO NOTHING;

-- 创建测试API管理数据（不同状态）
INSERT INTO api_management (
    id,
    name,
    description,
    type,
    status,
    project_id,
    created_at,
    updated_at
) VALUES 
-- 活跃API
('api_001', '测试API1', '活跃状态的测试API', 'REST', 'active', 'your_project_id_here', NOW(), NOW()),
('api_002', '测试API2', '活跃状态的测试API', 'GRAPHQL', 'active', 'your_project_id_here', NOW(), NOW()),

-- 非活跃API
('api_003', '测试API3', '非活跃状态的测试API', 'REST', 'inactive', 'your_project_id_here', NOW(), NOW()),
('api_004', '测试API4', '暂停状态的测试API', 'SOAP', 'suspended', 'your_project_id_here', NOW(), NOW()),
('api_005', '测试API5', '草稿状态的测试API', 'REST', 'draft', 'your_project_id_here', NOW(), NOW())

ON CONFLICT (id) DO NOTHING;

-- 验证测试数据
SELECT 
    '租户状态' as resource_type,
    id,
    name,
    status,
    is_active,
    CASE 
        WHEN status = 'active' AND is_active = true THEN '✅ 可创建配额'
        WHEN status = 'active' AND is_active = false THEN '❌ 未激活'
        ELSE '❌ 非活跃状态'
    END as validation_result
FROM tenants 
WHERE id IN ('tenant_001', 'tenant_002', 'tenant_003', 'tenant_004', 'tenant_005', 'tenant_006')
ORDER BY id;

SELECT 
    '应用状态' as resource_type,
    id,
    name,
    status,
    CASE 
        WHEN status = 'ACTIVE' THEN '✅ 可创建配额'
        ELSE '❌ 非活跃状态，将被跳过'
    END as validation_result
FROM applications 
WHERE id IN ('app_001', 'app_002', 'app_003', 'app_004', 'app_005')
ORDER BY id;

SELECT 
    'API状态' as resource_type,
    id,
    name,
    status,
    CASE 
        WHEN status = 'active' THEN '✅ 可创建配额'
        ELSE '❌ 非活跃状态，将被跳过'
    END as validation_result
FROM api_management 
WHERE id IN ('api_001', 'api_002', 'api_003', 'api_004', 'api_005')
ORDER BY id;

-- 测试场景说明
/*
测试场景1: 租户状态验证
- tenant_001: active + isActive=true  ✅ 可以创建配额
- tenant_002: active + isActive=true  ✅ 可以创建配额  
- tenant_003: active + isActive=true  ✅ 可以创建配额
- tenant_004: pending + isActive=false ❌ 租户状态非活跃，直接拒绝
- tenant_005: active + isActive=false  ❌ 租户未激活，直接拒绝
- tenant_006: suspended + isActive=false ❌ 租户已暂停，直接拒绝

测试场景2: 应用状态验证
- app_001: ACTIVE    ✅ 可以创建配额
- app_002: ACTIVE    ✅ 可以创建配额
- app_003: INACTIVE  ❌ 跳过，记录到skippedResources
- app_004: SUSPENDED ❌ 跳过，记录到skippedResources
- app_005: DRAFT     ❌ 跳过，记录到skippedResources

测试场景3: API状态验证
- api_001: active    ✅ 可以创建配额
- api_002: active    ✅ 可以创建配额
- api_003: inactive  ❌ 跳过，记录到skippedResources
- api_004: suspended ❌ 跳过，记录到skippedResources
- api_005: draft     ❌ 跳过，记录到skippedResources

测试场景4: 混合状态测试
为tenant_001创建配额分配，选择：
- app_001 (ACTIVE) + app_003 (INACTIVE) + app_005 (DRAFT)
- api_001 (active) + api_003 (inactive) + api_005 (draft)

预期结果：
- 成功创建: app_001, api_001 的配额分配
- 跳过资源: app_003, app_005, api_003, api_005
- 返回统计: created=2, skipped=4, total=6

测试场景5: 全部非活跃测试
为tenant_001创建配额分配，只选择：
- app_003 (INACTIVE) + app_004 (SUSPENDED)
- api_003 (inactive) + api_004 (suspended)

预期结果：
- 抛出CONFLICT错误
- 错误信息包含所有跳过的资源和原因
- 没有创建任何配额分配

测试场景6: 租户非活跃测试
为tenant_004 (pending状态) 创建配额分配

预期结果：
- 抛出BAD_REQUEST错误
- 错误信息: "无法为非活跃租户创建配额分配。租户状态：pending"
- 不会检查应用和API状态
*/
