-- Simple tenant tables creation
CREATE TYPE "TenantType" AS ENUM ('enterprise', 'standard', 'basic');
CREATE TYPE "TenantStatus" AS ENUM ('active', 'inactive', 'suspended', 'pending');
CREATE TYPE "TenantRole" AS ENUM ('owner', 'admin', 'member', 'viewer');

CREATE TABLE "tenants" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "display_name" TEXT,
    "description" TEXT,
    "type" "TenantType" NOT NULL DEFAULT 'standard',
    "category" TEXT,
    "contact_name" TEXT,
    "contact_email" TEXT,
    "contact_phone" TEXT,
    "address" TEXT,
    "website" TEXT,
    "license_number" TEXT,
    "tax_id" TEXT,
    "legal_person" TEXT,
    "status" "TenantStatus" NOT NULL DEFAULT 'pending',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,
    "verified_at" TIMESTAMP(3),
    "suspended_at" TIMESTAMP(3),
    "settings" JSONB,
    "metadata" JSONB,
    "max_users" INTEGER,
    "max_projects" INTEGER,
    "max_applications" INTEGER,
    "storage_limit" BIGINT,
    CONSTRAINT "tenants_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "tenant_organizations" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "org_id" TEXT NOT NULL,
    "role" "TenantRole" NOT NULL DEFAULT 'member',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "tenant_organizations_pkey" PRIMARY KEY ("id")
);

CREATE INDEX "tenants_name_idx" ON "tenants"("name");
CREATE INDEX "tenants_status_idx" ON "tenants"("status");
CREATE INDEX "tenants_type_idx" ON "tenants"("type");
CREATE UNIQUE INDEX "tenant_organizations_tenant_id_org_id_key" ON "tenant_organizations"("tenant_id", "org_id");
CREATE INDEX "tenant_organizations_org_id_idx" ON "tenant_organizations"("org_id");
CREATE INDEX "tenant_organizations_tenant_id_idx" ON "tenant_organizations"("tenant_id");

-- Insert sample data
INSERT INTO "tenants" ("id", "name", "display_name", "description", "type", "status", "contact_name", "contact_email") 
VALUES 
    ('tenant_1', 'demo-tenant', '演示租户', '这是一个演示租户', 'standard', 'active', '张三', '<EMAIL>'),
    ('tenant_2', 'test-tenant', '测试租户', '这是一个测试租户', 'enterprise', 'pending', '李四', '<EMAIL>');

-- Link tenants to organizations (assuming we have an organization)
INSERT INTO "tenant_organizations" ("id", "tenant_id", "org_id", "role") 
VALUES 
    ('tenant_org_1', 'tenant_1', 'cmf6fx2q40007camtlu5t8unh', 'member'),
    ('tenant_org_2', 'tenant_2', 'cmf6fx2q40007camtlu5t8unh', 'member');
