generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics", "relationJoins", "views"]
}

generator erd {
  provider     = "prisma-erd-generator"
  output       = "database.svg"
  disabled     = "true"
  ignoreEnums  = "true"
  ignoreTables = ["_prisma_migrations", "Session", "Account", "Example"]
}

generator kysely {
  provider = "prisma-kysely"
}

datasource db {
  provider          = "postgresql"
  url               = env("DATABASE_URL")
  directUrl         = env("DIRECT_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model Account {
  id                       String  @id @default(cuid())
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  userId                   String  @map("user_id")
  expires_in               Int?
  ext_expires_in           Int?
  refresh_token_expires_in Int?
  created_at               Int?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  expires      DateTime
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                        String                      @id @default(cuid())
  name                      String?
  email                     String?                     @unique
  emailVerified             DateTime?                   @map("email_verified")
  password                  String?
  image                     String?
  createdAt                 DateTime                    @default(now()) @map("created_at")
  updatedAt                 DateTime                    @default(now()) @updatedAt @map("updated_at")
  featureFlags              String[]                    @default([]) @map("feature_flags")
  admin                     Boolean                     @default(false)
  accounts                  Account[]
  sessions                  Session[]
  annotationQueueAssignment AnnotationQueueAssignment[]
  annotatedCompletedItem    AnnotationQueueItem[]       @relation("AnnotatorUser")
  annotatedLockedItem       AnnotationQueueItem[]       @relation("LockedByUser")
  dashboardWidgetsCreated   DashboardWidget[]           @relation("CreatedByUser")
  dashboardWidgetsUpdated   DashboardWidget[]           @relation("UpdatedByUser")
  dashboardCreated          Dashboard[]                 @relation("CreatedByUser")
  dashboardUpdated          Dashboard[]                 @relation("UpdatedByUser")
  invitations               MembershipInvitation[]
  organizationMemberships   OrganizationMembership[]
  projectMemberships        ProjectMembership[]
  surveys                   Survey[]
  tableViewPresetCreated    TableViewPreset[]           @relation("CreatedByUser")
  tableViewPresetUpdated    TableViewPreset[]           @relation("UpdatedByUser")

  ApprovalStep    ApprovalStep[]
  ApprovalComment ApprovalComment[]
  ApprovalRequest ApprovalRequest[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Organization {
  id                      String                   @id @default(cuid())
  name                    String
  createdAt               DateTime                 @default(now()) @map("created_at")
  updatedAt               DateTime                 @default(now()) @updatedAt @map("updated_at")
  cloudConfig             Json?                    @map("cloud_config")
  metadata                Json?
  ApiKey                  ApiKey[]
  MembershipInvitation    MembershipInvitation[]
  organizationMemberships OrganizationMembership[]
  projects                Project[]
  surveys                 Survey[]
  tenant_organizations    tenant_organizations[]

  @@map("organizations")
}

model Project {
  id                        String                      @id @default(cuid())
  createdAt                 DateTime                    @default(now()) @map("created_at")
  name                      String
  updatedAt                 DateTime                    @default(now()) @updatedAt @map("updated_at")
  orgId                     String                      @map("org_id")
  deletedAt                 DateTime?                   @map("deleted_at")
  retentionDays             Int?                        @map("retention_days")
  metadata                  Json?
  actions                   Action[]
  AnnotationQueueAssignment AnnotationQueueAssignment[]
  annotationQueueItem       AnnotationQueueItem[]
  annotationQueue           AnnotationQueue[]
  apiKeys                   ApiKey[]
  applications              Application[]
  automationExecutions      AutomationExecution[]
  Automation                Automation[]
  BatchExport               BatchExport[]
  BlobStorageIntegration    BlobStorageIntegration?
  comment                   Comment[]
  DashboardWidget           DashboardWidget[]
  Dashboard                 Dashboard[]
  dataset                   Dataset[]
  DefaultLlmModel           DefaultLlmModel?
  EvalTemplate              EvalTemplate[]
  JobConfiguration          JobConfiguration[]
  JobExecution              JobExecution[]
  LlmApiKeys                LlmApiKeys[]
  LlmSchema                 LlmSchema[]
  LlmTool                   LlmTool[]
  Media                     Media[]
  invitations               MembershipInvitation[]
  Model                     Model[]
  ObservationMedia          ObservationMedia[]
  LegacyObservation         LegacyPrismaObservation[]
  PendingDeletion           PendingDeletion[]
  PosthogIntegration        PosthogIntegration?
  Price                     Price[]
  projectMembers            ProjectMembership[]
  organization              Organization                @relation(fields: [orgId], references: [id], onDelete: Cascade)
  PromptDependency          PromptDependency[]
  PromptProtectedLabels     PromptProtectedLabels[]
  Prompt                    Prompt[]
  scoreConfig               ScoreConfig[]
  LegacyScore               LegacyPrismaScore[]
  SlackIntegration          SlackIntegration?
  TableViewPreset           TableViewPreset[]
  TraceMedia                TraceMedia[]
  sessions                  TraceSession[]
  LegacyTrace               LegacyPrismaTrace[]
  triggers                  Trigger[]
  ApiManagement             ApiManagement[]
  QuotaAllocation           QuotaAllocation[]
  ApprovalWorkflow          ApprovalWorkflow[]
  ApprovalRequest           ApprovalRequest[]

  @@index([orgId])
  @@map("projects")
}

model ApiKey {
  id                  String        @id @unique @default(cuid())
  createdAt           DateTime      @default(now()) @map("created_at")
  note                String?
  publicKey           String        @unique @map("public_key")
  hashedSecretKey     String        @unique @map("hashed_secret_key")
  displaySecretKey    String        @map("display_secret_key")
  lastUsedAt          DateTime?     @map("last_used_at")
  expiresAt           DateTime?     @map("expires_at")
  projectId           String?       @map("project_id")
  fastHashedSecretKey String?       @unique @map("fast_hashed_secret_key")
  orgId               String?       @map("organization_id")
  scope               ApiKeyScope   @default(PROJECT) @map("scope")
  organization        Organization? @relation(fields: [orgId], references: [id], onDelete: Cascade)
  project             Project?      @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([orgId])
  @@index([projectId])
  @@index([publicKey])
  @@index([hashedSecretKey])
  @@index([fastHashedSecretKey])
  @@map("api_keys")
}

model BackgroundMigration {
  id           String    @id @default(cuid())
  name         String    @unique
  script       String    @map("script")
  args         Json      @map("args")
  finishedAt   DateTime? @map("finished_at")
  failedAt     DateTime? @map("failed_at")
  failedReason String?   @map("failed_reason")
  workerId     String?   @map("worker_id")
  lockedAt     DateTime? @map("locked_at")
  state        Json      @default("{}") @map("state")

  @@map("background_migrations")
}

model LlmApiKeys {
  id                String            @id @unique @default(cuid())
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @default(now()) @updatedAt @map("updated_at")
  provider          String
  displaySecretKey  String            @map("display_secret_key")
  secretKey         String            @map("secret_key")
  projectId         String            @map("project_id")
  baseURL           String?           @map("base_url")
  adapter           String
  customModels      String[]          @default([]) @map("custom_models")
  withDefaultModels Boolean           @default(true) @map("with_default_models")
  config            Json?
  extraHeaders      String?           @map("extra_headers")
  extraHeaderKeys   String[]          @default([]) @map("extra_header_keys")
  DefaultLlmModel   DefaultLlmModel[] @relation("LlmApiKeyId")
  project           Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, provider])
  @@map("llm_api_keys")
}

model OrganizationMembership {
  id                 String              @id @default(cuid())
  orgId              String              @map("org_id")
  userId             String              @map("user_id")
  role               Role                @map("role")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @default(now()) @updatedAt @map("updated_at")
  organization       Organization        @relation(fields: [orgId], references: [id], onDelete: Cascade)
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  ProjectMemberships ProjectMembership[]

  @@unique([orgId, userId])
  @@index([userId])
  @@map("organization_memberships")
}

model ProjectMembership {
  projectId              String                 @map("project_id")
  userId                 String                 @map("user_id")
  createdAt              DateTime               @default(now()) @map("created_at")
  updatedAt              DateTime               @default(now()) @updatedAt @map("updated_at")
  orgMembershipId        String                 @map("org_membership_id")
  role                   Role
  organizationMembership OrganizationMembership @relation(fields: [orgMembershipId], references: [id], onDelete: Cascade)
  project                Project                @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user                   User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([projectId, userId])
  @@index([userId])
  @@index([projectId])
  @@index([orgMembershipId])
  @@map("project_memberships")
}

model MembershipInvitation {
  id              String       @id @unique @default(cuid())
  email           String
  projectId       String?      @map("project_id")
  invitedByUserId String?      @map("invited_by_user_id")
  createdAt       DateTime     @default(now()) @map("created_at")
  updatedAt       DateTime     @default(now()) @updatedAt @map("updated_at")
  orgId           String       @map("org_id")
  orgRole         Role         @map("org_role")
  projectRole     Role?        @map("project_role")
  invitedByUser   User?        @relation(fields: [invitedByUserId], references: [id])
  organization    Organization @relation(fields: [orgId], references: [id], onDelete: Cascade)
  project         Project?     @relation(fields: [projectId], references: [id])

  @@unique([email, orgId])
  @@index([projectId])
  @@index([orgId])
  @@index([email])
  @@map("membership_invitations")
}

model TraceSession {
  id          String   @default(cuid())
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  projectId   String   @map("project_id")
  bookmarked  Boolean  @default(false)
  public      Boolean  @default(false)
  environment String   @default("default")
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@id([id, projectId])
  @@index([projectId, createdAt(sort: Desc)])
  @@map("trace_sessions")
}

model LegacyPrismaTrace {
  id         String   @id @default(cuid())
  timestamp  DateTime @default(now())
  name       String?
  projectId  String   @map("project_id")
  metadata   Json?
  externalId String?  @map("external_id")
  userId     String?  @map("user_id")
  release    String?
  version    String?
  public     Boolean  @default(false)
  bookmarked Boolean  @default(false)
  input      Json?
  output     Json?
  sessionId  String?  @map("session_id")
  tags       String[] @default([])
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")
  project    Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, timestamp])
  @@index([sessionId])
  @@index([name])
  @@index([userId])
  @@index([id, userId])
  @@index([timestamp])
  @@index([createdAt])
  @@index([tags], type: Gin)
  @@map("traces")
}

model LegacyPrismaObservation {
  id                   String                       @id @default(cuid())
  name                 String?
  startTime            DateTime                     @default(now()) @map("start_time")
  endTime              DateTime?                    @map("end_time")
  parentObservationId  String?                      @map("parent_observation_id")
  type                 LegacyPrismaObservationType
  traceId              String?                      @map("trace_id")
  metadata             Json?
  model                String?
  modelParameters      Json?
  input                Json?
  output               Json?
  level                LegacyPrismaObservationLevel @default(DEFAULT)
  statusMessage        String?                      @map("status_message")
  completionStartTime  DateTime?                    @map("completion_start_time")
  completionTokens     Int                          @default(0) @map("completion_tokens")
  promptTokens         Int                          @default(0) @map("prompt_tokens")
  totalTokens          Int                          @default(0) @map("total_tokens")
  version              String?
  projectId            String                       @map("project_id")
  createdAt            DateTime                     @default(now()) @map("created_at")
  unit                 String?
  promptId             String?                      @map("prompt_id")
  inputCost            Decimal?                     @map("input_cost")
  outputCost           Decimal?                     @map("output_cost")
  totalCost            Decimal?                     @map("total_cost")
  internalModel        String?                      @map("internal_model")
  updatedAt            DateTime                     @default(now()) @updatedAt @map("updated_at")
  calculatedInputCost  Decimal?                     @map("calculated_input_cost")
  calculatedOutputCost Decimal?                     @map("calculated_output_cost")
  calculatedTotalCost  Decimal?                     @map("calculated_total_cost")
  internalModelId      String?                      @map("internal_model_id")
  project              Project                      @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([id, projectId])
  @@index([projectId, internalModel, startTime, unit])
  @@index([traceId, projectId, type, startTime])
  @@index([traceId, projectId, startTime])
  @@index([type])
  @@index([startTime])
  @@index([createdAt])
  @@index([model])
  @@index([internalModel])
  @@index([projectId, promptId])
  @@index([promptId])
  @@index([projectId, startTime, type])
  @@map("observations")
}

model LegacyPrismaScore {
  id            String                  @id @default(cuid())
  timestamp     DateTime                @default(now())
  name          String
  value         Float?
  observationId String?                 @map("observation_id")
  traceId       String                  @map("trace_id")
  comment       String?
  source        LegacyPrismaScoreSource
  projectId     String                  @map("project_id")
  authorUserId  String?                 @map("author_user_id")
  configId      String?                 @map("config_id")
  dataType      ScoreDataType           @default(NUMERIC) @map("data_type")
  stringValue   String?                 @map("string_value")
  createdAt     DateTime                @default(now()) @map("created_at")
  updatedAt     DateTime                @default(now()) @updatedAt @map("updated_at")
  queueId       String?                 @map("queue_id")
  scoreConfig   ScoreConfig?            @relation(fields: [configId], references: [id])
  project       Project                 @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([id, projectId])
  @@index([timestamp])
  @@index([value])
  @@index([projectId, name])
  @@index([authorUserId])
  @@index([configId])
  @@index([traceId], type: Hash)
  @@index([observationId], type: Hash)
  @@index([source])
  @@index([createdAt])
  @@map("scores")
}

model ScoreConfig {
  id          String              @id @default(cuid())
  createdAt   DateTime            @default(now()) @map("created_at")
  updatedAt   DateTime            @default(now()) @updatedAt @map("updated_at")
  projectId   String              @map("project_id")
  name        String
  dataType    ScoreDataType       @map("data_type")
  isArchived  Boolean             @default(false) @map("is_archived")
  minValue    Float?              @map("min_value")
  maxValue    Float?              @map("max_value")
  categories  Json?               @map("categories")
  description String?
  project     Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  legacyScore LegacyPrismaScore[]

  @@unique([id, projectId])
  @@index([dataType])
  @@index([isArchived])
  @@index([projectId])
  @@index([categories])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("score_configs")
}

model AnnotationQueue {
  id                        String                      @id @default(cuid())
  name                      String
  description               String?
  scoreConfigIds            String[]                    @default([]) @map("score_config_ids")
  projectId                 String                      @map("project_id")
  createdAt                 DateTime                    @default(now()) @map("created_at")
  updatedAt                 DateTime                    @default(now()) @updatedAt @map("updated_at")
  annotationQueueAssignment AnnotationQueueAssignment[]
  annotationQueueItem       AnnotationQueueItem[]
  project                   Project                     @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, name])
  @@index([id, projectId])
  @@index([projectId, createdAt])
  @@map("annotation_queues")
}

model AnnotationQueueItem {
  id              String                    @id @default(cuid())
  queueId         String                    @map("queue_id")
  objectId        String                    @map("object_id")
  objectType      AnnotationQueueObjectType @map("object_type")
  status          AnnotationQueueStatus     @default(PENDING)
  lockedAt        DateTime?                 @map("locked_at")
  lockedByUserId  String?                   @map("locked_by_user_id")
  annotatorUserId String?                   @map("annotator_user_id")
  completedAt     DateTime?                 @map("completed_at")
  projectId       String                    @map("project_id")
  createdAt       DateTime                  @default(now()) @map("created_at")
  updatedAt       DateTime                  @default(now()) @updatedAt @map("updated_at")
  annotatorUser   User?                     @relation("AnnotatorUser", fields: [annotatorUserId], references: [id])
  lockedByUser    User?                     @relation("LockedByUser", fields: [lockedByUserId], references: [id])
  project         Project                   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  queue           AnnotationQueue           @relation(fields: [queueId], references: [id], onDelete: Cascade)

  @@index([id, projectId])
  @@index([projectId, queueId, status])
  @@index([objectId, objectType, projectId, queueId])
  @@index([annotatorUserId])
  @@index([createdAt])
  @@map("annotation_queue_items")
}

model AnnotationQueueAssignment {
  id        String          @id @default(cuid())
  projectId String          @map("project_id")
  userId    String          @map("user_id")
  queueId   String          @map("queue_id")
  createdAt DateTime        @default(now()) @map("created_at")
  updatedAt DateTime        @default(now()) @updatedAt @map("updated_at")
  project   Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
  queue     AnnotationQueue @relation(fields: [queueId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, queueId, userId])
  @@map("annotation_queue_assignments")
}

model CronJobs {
  name         String    @id
  lastRun      DateTime? @map("last_run")
  state        String?
  jobStartedAt DateTime? @map("job_started_at")

  @@map("cron_jobs")
}

model Dataset {
  id                      String        @default(cuid())
  name                    String
  projectId               String        @map("project_id")
  createdAt               DateTime      @default(now()) @map("created_at")
  updatedAt               DateTime      @default(now()) @updatedAt @map("updated_at")
  description             String?
  metadata                Json?
  remoteExperimentPayload Json?         @map("remote_experiment_payload")
  remoteExperimentUrl     String?       @map("remote_experiment_url")
  datasetItems            DatasetItem[]
  datasetRuns             DatasetRuns[]
  project                 Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@id([id, projectId])
  @@unique([projectId, name])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("datasets")
}

model DatasetItem {
  id                  String            @default(cuid())
  input               Json?
  expectedOutput      Json?             @map("expected_output")
  sourceObservationId String?           @map("source_observation_id")
  datasetId           String            @map("dataset_id")
  createdAt           DateTime          @default(now()) @map("created_at")
  updatedAt           DateTime          @default(now()) @updatedAt @map("updated_at")
  status              DatasetStatus     @default(ACTIVE)
  sourceTraceId       String?           @map("source_trace_id")
  metadata            Json?
  projectId           String            @map("project_id")
  dataset             Dataset           @relation(fields: [datasetId, projectId], references: [id, projectId], onDelete: Cascade)
  datasetRunItems     DatasetRunItems[]

  @@id([id, projectId])
  @@index([sourceTraceId], type: Hash)
  @@index([sourceObservationId], type: Hash)
  @@index([datasetId], type: Hash)
  @@index([createdAt])
  @@index([updatedAt])
  @@map("dataset_items")
}

model DatasetRuns {
  id              String            @default(cuid())
  name            String
  datasetId       String            @map("dataset_id")
  createdAt       DateTime          @default(now()) @map("created_at")
  updatedAt       DateTime          @default(now()) @updatedAt @map("updated_at")
  metadata        Json?
  description     String?
  projectId       String            @map("project_id")
  datasetRunItems DatasetRunItems[]
  dataset         Dataset           @relation(fields: [datasetId, projectId], references: [id, projectId], onDelete: Cascade)

  @@id([id, projectId])
  @@unique([datasetId, projectId, name])
  @@index([datasetId], type: Hash)
  @@index([createdAt])
  @@index([updatedAt])
  @@map("dataset_runs")
}

model DatasetRunItems {
  id            String      @default(cuid())
  datasetRunId  String      @map("dataset_run_id")
  datasetItemId String      @map("dataset_item_id")
  observationId String?     @map("observation_id")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @default(now()) @updatedAt @map("updated_at")
  traceId       String      @map("trace_id")
  projectId     String      @map("project_id")
  datasetItem   DatasetItem @relation(fields: [datasetItemId, projectId], references: [id, projectId], onDelete: Cascade)
  datasetRun    DatasetRuns @relation(fields: [datasetRunId, projectId], references: [id, projectId], onDelete: Cascade)

  @@id([id, projectId])
  @@index([datasetRunId], type: Hash)
  @@index([datasetItemId], type: Hash)
  @@index([observationId], type: Hash)
  @@index([traceId])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("dataset_run_items")
}

model Comment {
  id           String            @id @default(cuid())
  projectId    String            @map("project_id")
  objectType   CommentObjectType @map("object_type")
  objectId     String            @map("object_id")
  createdAt    DateTime          @default(now()) @map("created_at")
  updatedAt    DateTime          @default(now()) @updatedAt @map("updated_at")
  content      String
  authorUserId String?           @map("author_user_id")
  project      Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, objectType, objectId])
  @@map("comments")
}

model Prompt {
  id               String             @id @default(cuid())
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  projectId        String             @map("project_id")
  createdBy        String             @map("created_by")
  name             String
  version          Int
  isActive         Boolean?           @map("is_active")
  config           Json               @default("{}") @db.Json
  prompt           Json
  type             String             @default("text")
  tags             String[]           @default([])
  labels           String[]           @default([])
  commitMessage    String?            @map("commit_message")
  PromptDependency PromptDependency[]
  project          Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, name, version])
  @@index([projectId, id])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([tags], type: Gin)
  @@map("prompts")
}

model PromptDependency {
  id           String   @id @default(cuid())
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")
  projectId    String   @map("project_id")
  parentId     String   @map("parent_id")
  childName    String   @map("child_name")
  childLabel   String?  @map("child_label")
  childVersion Int?     @map("child_version")
  parent       Prompt   @relation(fields: [parentId], references: [id], onDelete: Cascade)
  project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, parentId], map: "prompt_dependencies_project_id_parent_id")
  @@index([projectId, childName], map: "prompt_dependencies_project_id_child_name")
  @@map("prompt_dependencies")
}

model PromptProtectedLabels {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  projectId String   @map("project_id")
  label     String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, label])
  @@map("prompt_protected_labels")
}

model Model {
  id              String    @id @default(cuid())
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")
  projectId       String?   @map("project_id")
  modelName       String    @map("model_name")
  matchPattern    String    @map("match_pattern")
  startDate       DateTime? @map("start_date")
  inputPrice      Decimal?  @map("input_price")
  outputPrice     Decimal?  @map("output_price")
  totalPrice      Decimal?  @map("total_price")
  unit            String?
  tokenizerConfig Json?     @map("tokenizer_config")
  tokenizerId     String?   @map("tokenizer_id")
  project         Project?  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  Price           Price[]

  @@unique([projectId, modelName, startDate, unit])
  @@index([modelName])
  @@map("models")
}

model Price {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  modelId   String   @map("model_id")
  usageType String   @map("usage_type")
  price     Decimal
  projectId String?  @map("project_id")
  Model     Model    @relation(fields: [modelId], references: [id], onDelete: Cascade)
  project   Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([modelId, usageType])
  @@map("prices")
}

model AuditLog {
  id              String             @id @default(cuid())
  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @default(now()) @updatedAt @map("updated_at")
  userId          String?            @map("user_id")
  projectId       String?            @map("project_id")
  resourceType    String             @map("resource_type")
  resourceId      String             @map("resource_id")
  action          String
  before          String?
  after           String?
  orgId           String             @map("org_id")
  userOrgRole     String?            @map("user_org_role")
  userProjectRole String?            @map("user_project_role")
  apiKeyId        String?            @map("api_key_id")
  type            AuditLogRecordType @default(USER)

  @@index([projectId])
  @@index([apiKeyId])
  @@index([userId])
  @@index([orgId])
  @@index([createdAt])
  @@index([updatedAt])
  @@map("audit_logs")
}

model EvalTemplate {
  id               String             @id @default(cuid())
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  projectId        String?            @map("project_id")
  name             String
  version          Int
  prompt           String
  model            String?
  modelParams      Json?              @map("model_params")
  vars             String[]           @default([])
  outputSchema     Json               @map("output_schema")
  provider         String?
  partner          String?
  project          Project?           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  JobConfiguration JobConfiguration[]
  JobExecution     JobExecution[]

  @@unique([projectId, name, version])
  @@index([projectId, id])
  @@map("eval_templates")
}

model JobConfiguration {
  id              String         @id @default(cuid())
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @default(now()) @updatedAt @map("updated_at")
  projectId       String         @map("project_id")
  jobType         JobType        @map("job_type")
  evalTemplateId  String?        @map("eval_template_id")
  scoreName       String         @map("score_name")
  filter          Json
  targetObject    String         @map("target_object")
  variableMapping Json           @map("variable_mapping")
  sampling        Decimal
  delay           Int
  status          JobConfigState @default(ACTIVE)
  timeScope       String[]       @default(["NEW"]) @map("time_scope")
  evalTemplate    EvalTemplate?  @relation(fields: [evalTemplateId], references: [id])
  project         Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)
  JobExecution    JobExecution[]

  @@index([projectId, id])
  @@map("job_configurations")
}

model JobExecution {
  id                     String             @id @default(cuid())
  createdAt              DateTime           @default(now()) @map("created_at")
  updatedAt              DateTime           @default(now()) @updatedAt @map("updated_at")
  projectId              String             @map("project_id")
  jobConfigurationId     String             @map("job_configuration_id")
  status                 JobExecutionStatus
  startTime              DateTime?          @map("start_time")
  endTime                DateTime?          @map("end_time")
  error                  String?
  jobInputTraceId        String?            @map("job_input_trace_id")
  jobOutputScoreId       String?            @map("job_output_score_id")
  jobInputDatasetItemId  String?            @map("job_input_dataset_item_id")
  jobInputObservationId  String?            @map("job_input_observation_id")
  jobTemplateId          String?            @map("job_template_id")
  jobInputTraceTimestamp DateTime?          @map("job_input_trace_timestamp")
  jobConfiguration       JobConfiguration   @relation(fields: [jobConfigurationId], references: [id], onDelete: Cascade)
  jobTemplate            EvalTemplate?      @relation(fields: [jobTemplateId], references: [id], onUpdate: NoAction)
  project                Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, jobConfigurationId, jobInputTraceId])
  @@index([projectId, status])
  @@index([projectId, id])
  @@map("job_executions")
}

model DefaultLlmModel {
  id          String     @id @default(cuid())
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @default(now()) @updatedAt @map("updated_at")
  projectId   String     @unique @map("project_id")
  llmApiKeyId String     @map("llm_api_key_id")
  provider    String
  adapter     String
  model       String
  modelParams Json?      @map("model_params")
  LlmApiKey   LlmApiKeys @relation("LlmApiKeyId", fields: [llmApiKeyId], references: [id], onDelete: Cascade)
  Project     Project    @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("default_llm_models")
}

model SsoConfig {
  domain       String   @id @default(cuid())
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")
  authProvider String   @map("auth_provider")
  authConfig   Json?    @map("auth_config")

  @@map("sso_configs")
}

model PosthogIntegration {
  projectId              String    @id @map("project_id")
  encryptedPosthogApiKey String    @map("encrypted_posthog_api_key")
  posthogHostName        String    @map("posthog_host_name")
  lastSyncAt             DateTime? @map("last_sync_at")
  enabled                Boolean
  createdAt              DateTime  @default(now()) @map("created_at")
  project                Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("posthog_integrations")
}

model BlobStorageIntegration {
  projectId       String                         @id @map("project_id")
  type            BlobStorageIntegrationType     @map("type")
  bucketName      String                         @map("bucket_name")
  prefix          String                         @map("prefix")
  accessKeyId     String?                        @map("access_key_id")
  secretAccessKey String?                        @map("secret_access_key")
  region          String                         @map("region")
  endpoint        String?                        @map("endpoint")
  forcePathStyle  Boolean                        @map("force_path_style")
  nextSyncAt      DateTime?                      @map("next_sync_at")
  lastSyncAt      DateTime?                      @map("last_sync_at")
  enabled         Boolean
  exportFrequency String                         @map("export_frequency")
  createdAt       DateTime                       @default(now()) @map("created_at")
  updatedAt       DateTime                       @default(now()) @updatedAt @map("updated_at")
  fileType        BlobStorageIntegrationFileType @default(CSV) @map("file_type")
  exportMode      BlobStorageExportMode          @default(FULL_HISTORY) @map("export_mode")
  exportStartDate DateTime?                      @map("export_start_date")
  project         Project                        @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("blob_storage_integrations")
}

model BatchExport {
  id         String    @id @default(cuid())
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @default(now()) @updatedAt @map("updated_at")
  projectId  String    @map("project_id")
  userId     String    @map("user_id")
  finishedAt DateTime? @map("finished_at")
  expiresAt  DateTime? @map("expires_at")
  name       String
  status     String
  query      Json
  format     String
  url        String?
  log        String?
  project    Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, userId])
  @@index([status])
  @@map("batch_exports")
}

model Media {
  id               String
  sha256Hash       String             @map("sha_256_hash") @db.Char(44)
  projectId        String             @map("project_id")
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @default(now()) @updatedAt @map("updated_at")
  uploadedAt       DateTime?          @map("uploaded_at")
  uploadHttpStatus Int?               @map("upload_http_status")
  uploadHttpError  String?            @map("upload_http_error")
  bucketPath       String             @map("bucket_path")
  bucketName       String             @map("bucket_name")
  contentType      String             @map("content_type")
  contentLength    BigInt             @map("content_length")
  project          Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  ObservationMedia ObservationMedia[]
  TraceMedia       TraceMedia[]

  @@unique([projectId, id])
  @@unique([projectId, sha256Hash])
  @@map("media")
}

model TraceMedia {
  id        String   @id @default(cuid())
  projectId String   @map("project_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  mediaId   String   @map("media_id")
  traceId   String   @map("trace_id")
  field     String   @map("field")
  media     Media    @relation(fields: [mediaId, projectId], references: [id, projectId], onDelete: Cascade)
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, traceId, mediaId, field])
  @@index([projectId, mediaId])
  @@map("trace_media")
}

model ObservationMedia {
  id            String   @id @default(cuid())
  projectId     String   @map("project_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at")
  mediaId       String   @map("media_id")
  traceId       String   @map("trace_id")
  observationId String   @map("observation_id")
  field         String   @map("field")
  media         Media    @relation(fields: [mediaId, projectId], references: [id, projectId], onDelete: Cascade)
  project       Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, traceId, observationId, mediaId, field])
  @@index([projectId, mediaId])
  @@map("observation_media")
}

model BillingMeterBackup {
  stripeCustomerId String   @map("stripe_customer_id")
  meterId          String   @map("meter_id")
  startTime        DateTime @map("start_time")
  endTime          DateTime @map("end_time")
  aggregatedValue  Int      @map("aggregated_value")
  eventName        String   @map("event_name")
  orgId            String   @map("org_id")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([stripeCustomerId, meterId, startTime, endTime])
  @@index([stripeCustomerId, meterId, startTime, endTime])
  @@map("billing_meter_backups")
}

model LlmSchema {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  projectId   String   @map("project_id")
  name        String   @map("name")
  description String   @map("description")
  schema      Json     @map("schema") @db.Json
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, name])
  @@map("llm_schemas")
}

model LlmTool {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  projectId   String   @map("project_id")
  name        String   @map("name")
  description String   @map("description")
  parameters  Json     @map("parameters") @db.Json
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, name])
  @@map("llm_tools")
}

model Dashboard {
  id            String   @id @default(cuid())
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @updatedAt @map("updated_at")
  createdBy     String?  @map("created_by")
  updatedBy     String?  @map("updated_by")
  projectId     String?  @map("project_id")
  name          String   @map("name")
  description   String   @map("description")
  definition    Json     @map("definition")
  filters       Json     @default("[]") @map("filters")
  createdByUser User?    @relation("CreatedByUser", fields: [createdBy], references: [id])
  project       Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  updatedByUser User?    @relation("UpdatedByUser", fields: [updatedBy], references: [id])

  @@map("dashboards")
}

model DashboardWidget {
  id            String                   @id @default(cuid())
  createdAt     DateTime                 @default(now()) @map("created_at")
  updatedAt     DateTime                 @default(now()) @updatedAt @map("updated_at")
  createdBy     String?                  @map("created_by")
  updatedBy     String?                  @map("updated_by")
  projectId     String?                  @map("project_id")
  name          String                   @map("name")
  description   String                   @map("description")
  view          DashboardWidgetViews     @map("view")
  dimensions    Json                     @map("dimensions")
  metrics       Json                     @map("metrics")
  filters       Json                     @map("filters")
  chartType     DashboardWidgetChartType @map("chart_type")
  chartConfig   Json                     @map("chart_config")
  createdByUser User?                    @relation("CreatedByUser", fields: [createdBy], references: [id])
  project       Project?                 @relation(fields: [projectId], references: [id], onDelete: Cascade)
  updatedByUser User?                    @relation("UpdatedByUser", fields: [updatedBy], references: [id])

  @@map("dashboard_widgets")
}

model TableViewPreset {
  id               String   @id @default(cuid())
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at")
  projectId        String   @map("project_id")
  name             String   @map("name")
  tableName        String   @map("table_name")
  createdBy        String?  @map("created_by")
  updatedBy        String?  @map("updated_by")
  filters          Json     @map("filters")
  columnOrder      Json     @map("column_order")
  columnVisibility Json     @map("column_visibility")
  searchQuery      String?  @map("search_query")
  orderBy          Json?    @map("order_by")
  createdByUser    User?    @relation("CreatedByUser", fields: [createdBy], references: [id])
  project          Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  updatedByUser    User?    @relation("UpdatedByUser", fields: [updatedBy], references: [id])

  @@unique([projectId, tableName, name])
  @@map("table_view_presets")
}

model Action {
  id                   String                @id @default(cuid())
  createdAt            DateTime              @default(now()) @map("created_at")
  updatedAt            DateTime              @default(now()) @updatedAt @map("updated_at")
  projectId            String                @map("project_id")
  type                 ActionType
  config               Json
  project              Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)
  automationExecutions AutomationExecution[]
  automations          Automation[]

  @@index([projectId])
  @@map("actions")
}

model Trigger {
  id                   String                @id @default(cuid())
  createdAt            DateTime              @default(now()) @map("created_at")
  updatedAt            DateTime              @default(now()) @updatedAt @map("updated_at")
  projectId            String                @map("project_id")
  eventSource          String
  eventActions         String[]
  filter               Json?
  status               JobConfigState        @default(ACTIVE) @map("status")
  automationExecutions AutomationExecution[]
  automations          Automation[]
  project              Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@map("triggers")
}

model Automation {
  id                  String                @id @default(cuid())
  name                String                @map("name")
  triggerId           String                @map("trigger_id")
  actionId            String                @map("action_id")
  createdAt           DateTime              @default(now()) @map("created_at")
  projectId           String                @map("project_id")
  AutomationExecution AutomationExecution[]
  action              Action                @relation(fields: [actionId], references: [id], onDelete: Cascade)
  project             Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)
  trigger             Trigger               @relation(fields: [triggerId], references: [id], onDelete: Cascade)

  @@index([projectId, actionId, triggerId])
  @@index([projectId, name])
  @@map("automations")
}

model AutomationExecution {
  id           String                @id @default(cuid())
  createdAt    DateTime              @default(now()) @map("created_at")
  updatedAt    DateTime              @default(now()) @updatedAt @map("updated_at")
  sourceId     String                @map("source_id")
  automationId String                @map("automation_id")
  triggerId    String                @map("trigger_id")
  actionId     String                @map("action_id")
  projectId    String                @map("project_id")
  status       ActionExecutionStatus @default(PENDING) @map("status")
  input        Json                  @map("input")
  output       Json?                 @map("output")
  startedAt    DateTime?             @map("started_at")
  finishedAt   DateTime?             @map("finished_at")
  error        String?               @map("error")
  action       Action                @relation(fields: [actionId], references: [id], onDelete: Cascade)
  automation   Automation            @relation(fields: [automationId], references: [id], onDelete: Cascade)
  project      Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)
  trigger      Trigger               @relation(fields: [triggerId], references: [id], onDelete: Cascade)

  @@index([triggerId])
  @@index([actionId])
  @@index([projectId])
  @@map("automation_executions")
}

model SlackIntegration {
  id        String   @id @default(cuid())
  projectId String   @unique @map("project_id")
  teamId    String   @map("team_id")
  teamName  String   @map("team_name")
  botToken  String   @map("bot_token")
  botUserId String   @map("bot_user_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([teamId])
  @@map("slack_integrations")
}

model PendingDeletion {
  id        String   @id @default(cuid())
  projectId String   @map("project_id")
  object    String   @map("object")
  objectId  String   @map("object_id")
  isDeleted Boolean  @default(false) @map("is_deleted")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId, object, isDeleted])
  @@index([objectId, object])
  @@map("pending_deletions")
}

model Survey {
  id         String        @id @default(cuid())
  createdAt  DateTime      @default(now()) @map("created_at")
  surveyName SurveyName    @map("survey_name")
  response   Json
  userId     String?       @map("user_id")
  userEmail  String?       @map("user_email")
  orgId      String?       @map("org_id")
  org        Organization? @relation(fields: [orgId], references: [id], onDelete: Cascade)
  user       User?         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("surveys")
}

model Application {
  id              String               @id @default(cuid())
  projectId       String               @map("project_id")
  name            String
  description     String?
  type            ApplicationType
  category        String
  version         String               @default("1.0.0")
  developer       String
  tags            String[]             @default([])
  clientId        String               @unique @map("client_id")
  clientSecret    String               @map("client_secret")
  status          ApplicationStatus    @default(PENDING)
  isPublic        Boolean              @default(false) @map("is_public")
  autoApprove     Boolean              @default(false) @map("auto_approve")
  serviceConfig   Json?                @map("service_config")
  permissions     String[]             @default([])
  usageCount      Int                  @default(0) @map("usage_count")
  lastUsedAt      DateTime?            @map("last_used_at")
  createdAt       DateTime             @default(now()) @map("created_at")
  updatedAt       DateTime             @default(now()) @updatedAt @map("updated_at")
  createdBy       String?              @map("created_by")
  quotas          ApplicationQuota[]
  webhooks        ApplicationWebhook[]
  project         Project              @relation(fields: [projectId], references: [id], onDelete: Cascade)
  QuotaAllocation QuotaAllocation[]

  @@index([projectId])
  @@index([status])
  @@index([type])
  @@map("applications")
}

model ApplicationWebhook {
  id            String      @id @default(cuid())
  applicationId String      @map("application_id")
  url           String
  events        String[]    @default([])
  active        Boolean     @default(true)
  secretKey     String?     @map("secret_key")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @default(now()) @updatedAt @map("updated_at")
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@map("application_webhooks")
}

model ApplicationQuota {
  id            String      @id @default(cuid())
  applicationId String      @map("application_id")
  quotaType     String      @map("quota_type")
  limit         Int
  used          Int         @default(0)
  period        String      @default("MONTHLY")
  resetAt       DateTime?   @map("reset_at")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @default(now()) @updatedAt @map("updated_at")
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([quotaType])
  @@map("application_quotas")
}

model Tenant {
  id                   String                 @id @default(cuid())
  createdAt            DateTime               @default(now()) @map("created_at")
  updatedAt            DateTime               @default(now()) @updatedAt @map("updated_at")
  name                 String
  displayName          String?                @map("display_name")
  description          String?
  type                 TenantType
  category             String
  contactName          String                 @map("contact_name")
  contactEmail         String                 @map("contact_email")
  contactPhone         String?                @map("contact_phone")
  address              String?
  website              String?
  licenseNumber        String?                @unique @map("license_number")
  taxId                String?                @unique @map("tax_id")
  legalPerson          String?                @map("legal_person")
  status               TenantStatus           @default(pending)
  isActive             Boolean                @default(false) @map("is_active")
  isVerified           Boolean                @default(false) @map("is_verified")
  verifiedAt           DateTime?              @map("verified_at")
  suspendedAt          DateTime?              @map("suspended_at")
  settings             Json?
  metadata             Json?
  max_users            Int?
  max_projects         Int?
  max_applications     Int?
  storage_limit        Int?
  tenant_applications  tenant_applications[]
  tenant_audit_logs    tenant_audit_logs[]
  tenant_organizations tenant_organizations[]
  tenant_quotas        tenant_quotas[]
  QuotaAllocation      QuotaAllocation[]

  @@index([licenseNumber])
  @@index([status])
  @@index([type])
  @@map("tenants")
}

model tenant_applications {
  id                        String                      @id
  tenant_id                 String
  application_name          String
  application_type          ApplicationType
  description               String?
  business_case             String?
  expected_users            Int?
  status                    ApplicationRequestStatus    @default(draft)
  submitted_at              DateTime?
  reviewed_at               DateTime?
  approved_at               DateTime?
  rejected_at               DateTime?
  applicant_name            String
  applicant_email           String
  applicant_phone           String?
  reviewer_id               String?
  review_comments           String?
  attachments               Json?
  created_at                DateTime                    @default(now())
  updated_at                DateTime                    @default(now())
  tenants                   Tenant                      @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  tenant_approval_workflows tenant_approval_workflows[]

  @@index([application_type])
  @@index([status])
  @@index([tenant_id])
}

model tenant_approval_workflows {
  id                    String              @id
  tenant_application_id String
  step_order            Int
  step_name             String
  step_type             ApprovalStepType
  assignee_id           String?
  assignee_role         String?
  status                ApprovalStatus      @default(pending)
  started_at            DateTime?
  completed_at          DateTime?
  due_date              DateTime?
  decision              ApprovalDecision?
  comments              String?
  attachments           Json?
  created_at            DateTime            @default(now())
  updated_at            DateTime            @default(now())
  tenant_applications   tenant_applications @relation(fields: [tenant_application_id], references: [id], onDelete: Cascade)

  @@index([assignee_id])
  @@index([status])
  @@index([tenant_application_id])
}

model tenant_audit_logs {
  id            String   @id
  tenant_id     String
  action        String
  resource_type String
  resource_id   String
  user_id       String?
  user_email    String?
  user_role     String?
  details       Json?
  ip_address    String?
  user_agent    String?
  created_at    DateTime @default(now())
  tenants       Tenant   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@index([action])
  @@index([resource_type])
  @@index([tenant_id])
  @@index([user_id])
}

model tenant_organizations {
  id            String       @id
  tenant_id     String
  org_id        String
  role          TenantRole   @default(member)
  created_at    DateTime     @default(now())
  updated_at    DateTime     @default(now())
  organizations Organization @relation(fields: [org_id], references: [id], onDelete: Cascade)
  tenants       Tenant       @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@unique([tenant_id, org_id])
  @@index([org_id])
  @@index([tenant_id])
}

model tenant_quotas {
  id         String    @id
  tenant_id  String
  quota_type String
  limit      Int
  used       Int       @default(0)
  period     String    @default("MONTHLY")
  reset_at   DateTime?
  created_at DateTime  @default(now())
  updated_at DateTime  @default(now())
  tenants    Tenant    @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@unique([tenant_id, quota_type, period])
  @@index([quota_type])
  @@index([tenant_id])
}

model QuotaAllocation {
  id               String    @id @default(cuid())
  projectId        String    @map("project_id")
  resourceType     String    @map("resource_type") // TENANT, APPLICATION, API
  resourceId       String    @map("resource_id")
  quotaType        String    @map("quota_type") // API_CALLS, STORAGE, USERS, REQUESTS, BANDWIDTH, COMPUTE_TIME
  limit            Int
  used             Int       @default(0)
  period           String    @default("MONTHLY") // DAILY, WEEKLY, MONTHLY, YEARLY
  lifecyclePeriod  String    @default("ONE_YEAR") @map("lifecycle_period") // ONE_MONTH, SIX_MONTHS, ONE_YEAR, NEVER_EXPIRE
  warningThreshold Int       @default(80) @map("warning_threshold") // 警告阈值百分比
  status           String    @default("PENDING") @map("status") // PENDING, NORMAL, WARNING, EXCEEDED, SUSPENDED
  description      String?
  resetAt          DateTime? @map("reset_at")
  expiresAt        DateTime? @map("expires_at") // 生命周期过期时间
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at")

  // 关联关系
  project         Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)
  Application     Application?   @relation(fields: [applicationId], references: [id])
  applicationId   String?
  Tenant          Tenant?        @relation(fields: [tenantId], references: [id])
  tenantId        String?
  ApiManagement   ApiManagement? @relation(fields: [apiManagementId], references: [id])
  apiManagementId String?

  @@unique([projectId, tenantId, resourceType, resourceId, quotaType])
  @@index([projectId])
  @@index([resourceType])
  @@index([resourceId])
  @@index([quotaType])
  @@index([status])
  @@map("quota_allocations")
}

model ApprovalWorkflow {
  id          String   @id @default(cuid())
  projectId   String   @map("project_id")
  name        String
  description String?
  type        String // TENANT_REGISTRATION, APPLICATION_REGISTRATION, QUOTA_REQUEST
  isActive    Boolean  @default(true) @map("is_active")
  steps       Json // 审批步骤配置
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  project  Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  requests ApprovalRequest[]

  @@index([projectId])
  @@index([type])
  @@map("approval_workflows")
}

model ApprovalRequest {
  id          String    @id @default(cuid())
  projectId   String    @map("project_id")
  workflowId  String    @map("workflow_id")
  requestType String    @map("request_type") // TENANT_REGISTRATION, APPLICATION_REGISTRATION, QUOTA_REQUEST
  resourceId  String    @map("resource_id") // 关联的资源ID
  requesterId String    @map("requester_id")
  title       String
  description String?
  formData    Json      @map("form_data") // 申请表单数据
  attachments Json? // 附件信息
  status      String    @default("PENDING") // PENDING, IN_PROGRESS, APPROVED, REJECTED, CANCELLED
  currentStep Int       @default(0) @map("current_step")
  priority    String    @default("NORMAL") // LOW, NORMAL, HIGH, URGENT
  dueDate     DateTime? @map("due_date")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")

  project   Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  workflow  ApprovalWorkflow  @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  requester User              @relation(fields: [requesterId], references: [id], onDelete: Cascade)
  steps     ApprovalStep[]
  comments  ApprovalComment[]

  @@index([projectId])
  @@index([workflowId])
  @@index([requesterId])
  @@index([status])
  @@index([requestType])
  @@map("approval_requests")
}

model ApprovalStep {
  id           String    @id @default(cuid())
  requestId    String    @map("request_id")
  stepOrder    Int       @map("step_order")
  stepName     String    @map("step_name")
  stepType     String    @map("step_type") // USER, ROLE, AUTO
  assigneeId   String?   @map("assignee_id")
  assigneeRole String?   @map("assignee_role")
  status       String    @default("PENDING") // PENDING, IN_PROGRESS, APPROVED, REJECTED, SKIPPED
  decision     String? // APPROVE, REJECT, DELEGATE
  comment      String?
  processedAt  DateTime? @map("processed_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at")

  request  ApprovalRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
  assignee User?           @relation(fields: [assigneeId], references: [id], onDelete: SetNull)

  @@index([requestId])
  @@index([assigneeId])
  @@index([status])
  @@map("approval_steps")
}

model ApprovalComment {
  id         String   @id @default(cuid())
  requestId  String   @map("request_id")
  userId     String   @map("user_id")
  content    String
  isInternal Boolean  @default(false) @map("is_internal") // 是否为内部评论
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")

  request ApprovalRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([requestId])
  @@index([userId])
  @@map("approval_comments")
}

enum ApiKeyScope {
  ORGANIZATION
  PROJECT
}

enum Role {
  OWNER
  ADMIN
  MEMBER
  VIEWER
  NONE
}

enum LegacyPrismaObservationType {
  SPAN
  EVENT
  GENERATION
  AGENT
  TOOL
  CHAIN
  RETRIEVER
  EVALUATOR
  EMBEDDING
  GUARDRAIL

  @@map("ObservationType")
}

enum LegacyPrismaObservationLevel {
  DEBUG
  DEFAULT
  WARNING
  ERROR

  @@map("ObservationLevel")
}

enum LegacyPrismaScoreSource {
  ANNOTATION
  API
  EVAL

  @@map("ScoreSource")
}

enum ScoreDataType {
  CATEGORICAL
  NUMERIC
  BOOLEAN
}

enum AnnotationQueueStatus {
  PENDING
  COMPLETED
}

enum AnnotationQueueObjectType {
  TRACE
  OBSERVATION
  SESSION
}

enum DatasetStatus {
  ACTIVE
  ARCHIVED
}

enum CommentObjectType {
  TRACE
  OBSERVATION
  SESSION
  PROMPT
}

enum AuditLogRecordType {
  USER
  API_KEY
}

enum JobType {
  EVAL
}

enum JobConfigState {
  ACTIVE
  INACTIVE
}

enum JobExecutionStatus {
  COMPLETED
  ERROR
  PENDING
  CANCELLED
}

enum BlobStorageIntegrationFileType {
  JSON
  CSV
  JSONL

  @@map("BlobStorageIntegrationFileType")
}

enum BlobStorageIntegrationType {
  S3
  S3_COMPATIBLE
  AZURE_BLOB_STORAGE

  @@map("BlobStorageIntegrationType")
}

enum BlobStorageExportMode {
  FULL_HISTORY
  FROM_TODAY
  FROM_CUSTOM_DATE

  @@map("BlobStorageExportMode")
}

enum DashboardWidgetViews {
  TRACES
  OBSERVATIONS
  SCORES_NUMERIC
  SCORES_CATEGORICAL
}

enum DashboardWidgetChartType {
  LINE_TIME_SERIES
  BAR_TIME_SERIES
  HORIZONTAL_BAR
  VERTICAL_BAR
  PIE
  NUMBER
  HISTOGRAM
  PIVOT_TABLE
}

enum ActionType {
  WEBHOOK
  SLACK
}

enum ActionExecutionStatus {
  COMPLETED
  ERROR
  PENDING
  CANCELLED
}

enum SurveyName {
  ORG_ONBOARDING  @map("org_onboarding")
  USER_ONBOARDING @map("user_onboarding")

  @@map("SurveyName")
}

enum ApplicationType {
  ROBOT_APPLICATION            @map("robot_application")
  QUALITY_CONTROL_APP          @map("quality_control_app")
  DOCUMENT_GENERATION_APP      @map("document_generation_app")
  INTELLIGENT_CUSTOMER_SERVICE @map("intelligent_customer_service")
  INTELLIGENT_AGENT_APP        @map("intelligent_agent_app")
  CUSTOM_APPLICATION           @map("custom_application")

  @@map("ApplicationType")
}

enum ApplicationStatus {
  PENDING   @map("pending")
  ACTIVE    @map("active")
  INACTIVE  @map("inactive")
  SUSPENDED @map("suspended")
  REJECTED  @map("rejected")

  @@map("ApplicationStatus")
}

enum ApplicationRequestStatus {
  draft
  submitted
  reviewing
  approved
  rejected
  withdrawn
  expired
}

enum ApprovalDecision {
  approve
  reject
  return
}

enum ApprovalStatus {
  pending
  in_progress
  completed
  skipped
  expired
}

enum ApprovalStepType {
  manual
  automatic
  conditional
}

enum TenantRole {
  owner
  admin
  member
  viewer
}

enum TenantStatus {
  pending
  active
  inactive
  suspended
  rejected
  expired
}

enum TenantType {
  hospital_tertiary
  hospital_secondary
  hospital_primary
  hospital_specialized
  clinic
  health_center
  medical_group
  other
}

model ApiManagement {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  projectId   String    @map("project_id")
  name        String
  description String?
  type        ApiType
  status      ApiStatus @default(active)

  // OpenAI Compatible API fields
  baseUrl   String? @map("base_url")
  modelName String? @map("model_name")
  authKey   String? @map("auth_key")

  // Ragflow Agent API fields
  agentId String? @map("agent_id")
  address String?
  apiKey  String? @map("api_key")

  // Dify API fields
  difyBaseUrl String? @map("dify_base_url")
  difyApiKey  String? @map("dify_api_key")
  difyAppId   String? @map("dify_app_id")

  // Other API fields
  customConfig Json? @map("custom_config")
  headers      Json? // Custom headers

  // Metadata
  metadata Json?
  tags     String[] @default([])

  project         Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  QuotaAllocation QuotaAllocation[]

  @@index([projectId])
  @@index([type])
  @@index([status])
  @@map("api_management")
}

enum ApiType {
  openai_compatible
  ragflow_agent
  dify
  other
}

enum ApiStatus {
  active
  inactive
  testing
  deprecated
}
