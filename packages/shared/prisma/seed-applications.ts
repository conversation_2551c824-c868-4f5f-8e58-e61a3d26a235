import {
  PrismaClient,
  ApplicationType,
  ApplicationStatus,
} from "@prisma/client";
import { randomBytes } from "crypto";

const prisma = new PrismaClient();

// 生成客户端凭据
function generateClientCredentials() {
  const clientId = `app_${randomBytes(16).toString("hex")}`;
  const clientSecret = `sk_${randomBytes(32).toString("hex")}`;
  return { clientId, clientSecret };
}

async function seedApplications() {
  console.log("开始创建应用注册测试数据...");

  try {
    // 查找第一个项目
    const project = await prisma.project.findFirst();
    if (!project) {
      console.log("未找到项目，请先创建项目");
      return;
    }

    console.log(`使用项目: ${project.name} (${project.id})`);

    // 创建测试应用
    const applications = [
      {
        name: "机器人应用",
        description: "智能语音识别和翻译服务，支持多种语言实时翻译",
        type: ApplicationType.ROBOT_APPLICATION,
        category: "AI服务",
        version: "1.2.0",
        developer: "AI科技公司",
        tags: ["语音识别", "机器翻译", "多语言"],
        isPublic: true,
        autoApprove: false,
        status: ApplicationStatus.ACTIVE,
        serviceConfig: {
          endpoint: "https://api.robot-app.com/v1",
          timeout: 30000,
          retryCount: 3,
          rateLimit: 1000,
        },
        permissions: ["speech:recognize", "translate:text", "translate:audio"],
        usageCount: 1250,
      },
      {
        name: "质控应用",
        description: "医疗病历质量控制和审核系统",
        type: ApplicationType.QUALITY_CONTROL_APP,
        category: "医疗健康",
        version: "2.1.0",
        developer: "医疗科技有限公司",
        tags: ["病历管理", "质量控制", "医疗审核"],
        isPublic: false,
        autoApprove: true,
        status: ApplicationStatus.ACTIVE,
        serviceConfig: {
          endpoint: "https://api.qc-app.com/v2",
          timeout: 45000,
          retryCount: 2,
          rateLimit: 500,
        },
        permissions: ["medical:read", "medical:write", "audit:create"],
        usageCount: 890,
      },
      {
        name: "文书生成应用",
        description: "基于AI的医疗文书自动生成和编辑",
        type: ApplicationType.DOCUMENT_GENERATION_APP,
        category: "医疗健康",
        version: "1.5.0",
        developer: "医疗AI公司",
        tags: ["自动生成", "文书编辑", "AI辅助"],
        isPublic: true,
        autoApprove: false,
        status: ApplicationStatus.ACTIVE,
        serviceConfig: {
          endpoint: "https://api.doc-gen.com/v1",
          timeout: 60000,
          retryCount: 3,
          rateLimit: 200,
        },
        permissions: ["document:generate", "document:edit", "ai:access"],
        usageCount: 650,
      },
      {
        name: "智能客服系统",
        description: "AI驱动的客户服务和支持平台",
        type: ApplicationType.INTELLIGENT_CUSTOMER_SERVICE,
        category: "客户服务",
        version: "2.0.0",
        developer: "客服科技公司",
        tags: ["智能客服", "自动回复", "客户支持"],
        isPublic: true,
        autoApprove: true,
        status: ApplicationStatus.ACTIVE,
        serviceConfig: {
          endpoint: "https://api.customer-service.com/v2",
          timeout: 30000,
          retryCount: 3,
          rateLimit: 2000,
        },
        permissions: ["chat:read", "chat:write", "customer:access"],
        usageCount: 1100,
      },
      {
        name: "智能体应用",
        description: "社区健康管理智能代理系统",
        type: ApplicationType.INTELLIGENT_AGENT_APP,
        category: "医疗健康",
        version: "1.0.0",
        developer: "健康科技公司",
        tags: ["社区健康", "智能代理", "健康管理"],
        isPublic: false,
        autoApprove: false,
        status: ApplicationStatus.PENDING,
        serviceConfig: {
          endpoint: "https://api.health-agent.com/v1",
          timeout: 30000,
          retryCount: 3,
          rateLimit: 100,
        },
        permissions: ["health:read", "agent:control", "community:access"],
        usageCount: 0,
      },
      {
        name: "自定义应用",
        description: "根据特定需求定制开发的应用程序",
        type: ApplicationType.CUSTOM_APPLICATION,
        category: "自定义",
        version: "1.1.0",
        developer: "定制开发公司",
        tags: ["定制开发", "特定需求", "灵活配置"],
        isPublic: false,
        autoApprove: false,
        status: ApplicationStatus.ACTIVE,
        serviceConfig: {
          endpoint: "https://api.custom-app.com/v1",
          timeout: 30000,
          retryCount: 3,
          rateLimit: 300,
        },
        permissions: ["custom:access", "config:manage"],
        usageCount: 320,
      },
    ];

    // 创建应用
    for (const appData of applications) {
      const { clientId, clientSecret } = generateClientCredentials();

      const application = await prisma.application.create({
        data: {
          ...appData,
          projectId: project.id,
          clientId,
          clientSecret,
          createdBy: "seed-script",
        },
      });

      console.log(`✓ 创建应用: ${application.name} (${application.id})`);

      // 为每个应用创建一些Webhook
      if (Math.random() > 0.5) {
        const webhook = await prisma.applicationWebhook.create({
          data: {
            applicationId: application.id,
            url: `https://webhook.example.com/${application.id}`,
            events: ["application.updated", "quota.exceeded", "api.error"],
            active: true,
            secretKey: `whsec_${randomBytes(32).toString("hex")}`,
          },
        });
        console.log(`  ✓ 创建Webhook: ${webhook.url}`);
      }

      // 为每个应用创建配额
      const quotaTypes = ["API_CALLS", "STORAGE", "USERS"];
      const randomQuotaType =
        quotaTypes[Math.floor(Math.random() * quotaTypes.length)];

      const quota = await prisma.applicationQuota.create({
        data: {
          applicationId: application.id,
          quotaType: randomQuotaType,
          limit: Math.floor(Math.random() * 10000) + 1000,
          used: Math.floor(Math.random() * 500),
          period: "MONTHLY",
          resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
        },
      });
      console.log(
        `  ✓ 创建配额: ${quota.quotaType} (${quota.used}/${quota.limit})`,
      );
    }

    console.log("\n✅ 应用注册测试数据创建完成！");
    console.log(`总共创建了 ${applications.length} 个应用`);
  } catch (error) {
    console.error("创建测试数据时出错:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行种子脚本
if (require.main === module) {
  seedApplications();
}

export { seedApplications };
