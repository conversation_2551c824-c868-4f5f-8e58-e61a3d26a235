-- CreateEnum
CREATE TYPE "ApiType" AS ENUM ('openai_compatible', 'ragflow_agent', 'dify', 'other');

-- CreateEnum
CREATE TYPE "ApiStatus" AS ENUM ('active', 'inactive', 'testing', 'deprecated');

-- CreateTable
CREATE TABLE "api_management" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "project_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "ApiType" NOT NULL,
    "status" "ApiStatus" NOT NULL DEFAULT 'active',
    "base_url" TEXT,
    "model_name" TEXT,
    "auth_key" TEXT,
    "agent_id" TEXT,
    "address" TEXT,
    "api_key" TEXT,
    "dify_base_url" TEXT,
    "dify_api_key" TEXT,
    "dify_app_id" TEXT,
    "custom_config" JSONB,
    "headers" JSONB,
    "metadata" JSONB,
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "api_management_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "api_management_project_id_idx" ON "api_management"("project_id");

-- CreateIndex
CREATE INDEX "api_management_type_idx" ON "api_management"("type");

-- CreateIndex
CREATE INDEX "api_management_status_idx" ON "api_management"("status");

-- AddForeignKey
ALTER TABLE "api_management" ADD CONSTRAINT "api_management_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;
