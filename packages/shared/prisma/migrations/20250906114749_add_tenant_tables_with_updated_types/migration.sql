-- CreateEnum
CREATE TYPE "ApplicationRequestStatus" AS ENUM ('draft', 'submitted', 'reviewing', 'approved', 'rejected', 'withdrawn', 'expired');

-- CreateEnum
CREATE TYPE "ApprovalDecision" AS ENUM ('approve', 'reject', 'return');

-- CreateEnum
CREATE TYPE "ApprovalStatus" AS ENUM ('pending', 'in_progress', 'completed', 'skipped', 'expired');

-- CreateEnum
CREATE TYPE "ApprovalStepType" AS ENUM ('manual', 'automatic', 'conditional');

-- CreateEnum
CREATE TYPE "TenantRole" AS ENUM ('owner', 'admin', 'member', 'viewer');

-- CreateEnum
CREATE TYPE "TenantStatus" AS ENUM ('pending', 'active', 'inactive', 'suspended', 'rejected', 'expired');

-- CreateEnum
CREATE TYPE "TenantType" AS ENUM ('hospital_tertiary', 'hospital_secondary', 'hospital_primary', 'hospital_specialized', 'clinic', 'health_center', 'medical_group', 'other');

-- CreateTable
CREATE TABLE "tenants" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "display_name" TEXT,
    "description" TEXT,
    "type" "TenantType" NOT NULL,
    "category" TEXT NOT NULL,
    "contact_name" TEXT NOT NULL,
    "contact_email" TEXT NOT NULL,
    "contact_phone" TEXT,
    "address" TEXT,
    "website" TEXT,
    "license_number" TEXT,
    "tax_id" TEXT,
    "legal_person" TEXT,
    "status" "TenantStatus" NOT NULL DEFAULT 'pending',
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,
    "verified_at" TIMESTAMP(3),
    "suspended_at" TIMESTAMP(3),
    "settings" JSONB,
    "metadata" JSONB,
    "max_users" INTEGER,
    "max_projects" INTEGER,
    "max_applications" INTEGER,
    "storage_limit" INTEGER,

    CONSTRAINT "tenants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_applications" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "application_name" TEXT NOT NULL,
    "application_type" "ApplicationType" NOT NULL,
    "description" TEXT,
    "business_case" TEXT,
    "expected_users" INTEGER,
    "status" "ApplicationRequestStatus" NOT NULL DEFAULT 'draft',
    "submitted_at" TIMESTAMP(3),
    "reviewed_at" TIMESTAMP(3),
    "approved_at" TIMESTAMP(3),
    "rejected_at" TIMESTAMP(3),
    "applicant_name" TEXT NOT NULL,
    "applicant_email" TEXT NOT NULL,
    "applicant_phone" TEXT,
    "reviewer_id" TEXT,
    "review_comments" TEXT,
    "attachments" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tenant_applications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_approval_workflows" (
    "id" TEXT NOT NULL,
    "tenant_application_id" TEXT NOT NULL,
    "step_order" INTEGER NOT NULL,
    "step_name" TEXT NOT NULL,
    "step_type" "ApprovalStepType" NOT NULL,
    "assignee_id" TEXT,
    "assignee_role" TEXT,
    "status" "ApprovalStatus" NOT NULL DEFAULT 'pending',
    "started_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "due_date" TIMESTAMP(3),
    "decision" "ApprovalDecision",
    "comments" TEXT,
    "attachments" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tenant_approval_workflows_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_audit_logs" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "resource_type" TEXT NOT NULL,
    "resource_id" TEXT NOT NULL,
    "user_id" TEXT,
    "user_email" TEXT,
    "user_role" TEXT,
    "details" JSONB,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tenant_audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_organizations" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "org_id" TEXT NOT NULL,
    "role" "TenantRole" NOT NULL DEFAULT 'member',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tenant_organizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tenant_quotas" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "quota_type" TEXT NOT NULL,
    "limit" INTEGER NOT NULL,
    "used" INTEGER NOT NULL DEFAULT 0,
    "period" TEXT NOT NULL DEFAULT 'MONTHLY',
    "reset_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tenant_quotas_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tenants_license_number_key" ON "tenants"("license_number");

-- CreateIndex
CREATE UNIQUE INDEX "tenants_tax_id_key" ON "tenants"("tax_id");

-- CreateIndex
CREATE INDEX "tenants_license_number_idx" ON "tenants"("license_number");

-- CreateIndex
CREATE INDEX "tenants_status_idx" ON "tenants"("status");

-- CreateIndex
CREATE INDEX "tenants_type_idx" ON "tenants"("type");

-- CreateIndex
CREATE INDEX "tenant_applications_application_type_idx" ON "tenant_applications"("application_type");

-- CreateIndex
CREATE INDEX "tenant_applications_status_idx" ON "tenant_applications"("status");

-- CreateIndex
CREATE INDEX "tenant_applications_tenant_id_idx" ON "tenant_applications"("tenant_id");

-- CreateIndex
CREATE INDEX "tenant_approval_workflows_assignee_id_idx" ON "tenant_approval_workflows"("assignee_id");

-- CreateIndex
CREATE INDEX "tenant_approval_workflows_status_idx" ON "tenant_approval_workflows"("status");

-- CreateIndex
CREATE INDEX "tenant_approval_workflows_tenant_application_id_idx" ON "tenant_approval_workflows"("tenant_application_id");

-- CreateIndex
CREATE INDEX "tenant_audit_logs_action_idx" ON "tenant_audit_logs"("action");

-- CreateIndex
CREATE INDEX "tenant_audit_logs_resource_type_idx" ON "tenant_audit_logs"("resource_type");

-- CreateIndex
CREATE INDEX "tenant_audit_logs_tenant_id_idx" ON "tenant_audit_logs"("tenant_id");

-- CreateIndex
CREATE INDEX "tenant_audit_logs_user_id_idx" ON "tenant_audit_logs"("user_id");

-- CreateIndex
CREATE INDEX "tenant_organizations_org_id_idx" ON "tenant_organizations"("org_id");

-- CreateIndex
CREATE INDEX "tenant_organizations_tenant_id_idx" ON "tenant_organizations"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_organizations_tenant_id_org_id_key" ON "tenant_organizations"("tenant_id", "org_id");

-- CreateIndex
CREATE INDEX "tenant_quotas_quota_type_idx" ON "tenant_quotas"("quota_type");

-- CreateIndex
CREATE INDEX "tenant_quotas_tenant_id_idx" ON "tenant_quotas"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_quotas_tenant_id_quota_type_period_key" ON "tenant_quotas"("tenant_id", "quota_type", "period");

-- AddForeignKey
ALTER TABLE "tenant_applications" ADD CONSTRAINT "tenant_applications_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_approval_workflows" ADD CONSTRAINT "tenant_approval_workflows_tenant_application_id_fkey" FOREIGN KEY ("tenant_application_id") REFERENCES "tenant_applications"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_audit_logs" ADD CONSTRAINT "tenant_audit_logs_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_organizations" ADD CONSTRAINT "tenant_organizations_org_id_fkey" FOREIGN KEY ("org_id") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_organizations" ADD CONSTRAINT "tenant_organizations_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_quotas" ADD CONSTRAINT "tenant_quotas_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;
