{"loading": "loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "copy": "Copy", "copied": "<PERSON>pied", "share": "Share", "settings": "Settings", "help": "Help", "documentation": "Documentation", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "version": "Version", "name": "Name", "description": "Description", "type": "Type", "status": "Status", "date": "Date", "time": "Time", "created": "Created", "updated": "Updated", "modified": "Modified", "actions": "Actions", "options": "Options", "details": "Details", "overview": "Overview", "summary": "Summary", "total": "Total", "count": "Count", "size": "Size", "duration": "Duration", "cost": "Cost", "usage": "Usage", "limit": "Limit", "quota": "<PERSON><PERSON><PERSON>", "remaining": "Remaining", "expired": "Expired", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "public": "Public", "private": "Private", "visible": "Visible", "hidden": "Hidden", "required": "Required", "optional": "Optional", "all": "All", "none": "None", "other": "Other", "unknown": "Unknown", "empty": "Empty", "noData": "No data available", "noResults": "No results.", "notFound": "Not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "serverError": "Server error", "networkError": "Network error", "tryAgain": "Try again", "refresh": "Refresh", "reload": "Reload", "rows": "Rows", "rowsPerPage": "Rows per page", "page": "Page", "of": "of", "goToFirstPage": "Go to first page", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page", "goToLastPage": "Go to last page", "common": "Common", "form": "Form", "goHome": "Go to Homepage", "organizations": "Organizations", "organizationsDescription": "Organizations help you manage access to projects. Each organization can have multiple projects and team members with different roles.", "searchProjects": "Search projects", "newOrganization": "New Organization", "getStarted": "Get Started", "onboardingDescriptionCanCreate": "Create an organization to get started. Alternatively, ask your organization admin to invite you.", "onboardingDescriptionNeedInvite": "You need to get invited to an organization to get started with <PERSON><PERSON>.", "docs": "Docs", "askAI": "Ask AI", "tryLangfuseDemo": "<PERSON>", "demoDescription": "We have built a Q&A chatbot that answers questions based on the Langfuse Docs. Interact with it to see traces in Langfuse.", "viewDemoProject": "View Demo Project", "demoOrganization": "Demo Organization", "organization": "Organization", "goTo": "Go to...", "projects": "Projects", "home": "Home", "dashboards": "Dashboards", "tracing": "Tracing", "sessions": "Sessions", "users": "Users", "prompts": "Prompts", "playground": "Playground", "scores": "Scores", "llmAsJudge": "LLM-as-a-Judge", "humanAnnotation": "Human Annotation", "datasets": "Datasets", "upgrade": "Upgrade", "cloudStatus": "Cloud Status", "theme": "Theme", "signOut": "Sign out", "chat": "Cha<PERSON>", "contactSupport": "Contact Support", "githubSupport": "GitHub Support", "discord": "Discord", "statusPage": "Status Page", "featureRequest": "Feature Request", "reportBug": "Report a Bug", "columns": "Columns", "columnVisibility": "Column Visibility", "showHideColumns": "Show/hide columns", "columnCannotBeHidden": "This column may not be hidden", "dragToReorderColumns": "Drag and drop to reorder columns", "selectAll": "Select All", "deselectAll": "Deselect All", "selectAllColumns": "Select All Columns", "deselectAllColumns": "Deselect All Columns", "restoreDefaults": "<PERSON><PERSON>", "sortByColumn": "Sort by this column", "fullText": "Full Text", "idsNames": "IDs / Names", "pleaseConfirm": "Please confirm", "deleteWarning": "This action cannot be undone and removes all the data associated with this {{entityName}}.", "typeToConfirm": "Type \"{{confirmation}}\" to confirm", "pleaseTypeCorrectConfirmation": "Please type the correct confirmation", "deleteEntity": "Delete {{entityName}}", "copyCode": "Copy code", "observability": "Observability", "promptManagement": "Prompt Management", "evaluation": "Evaluation", "registrationManagement": "Registration Management", "applicationRegistration": "Application Registration", "tenantRegistration": "Tenant Management", "quotaManagement": "Quota Management", "authorizationManagement": "Authorization Management", "apiManagement": "API Management", "configureTracing": "Configure Tracing"}