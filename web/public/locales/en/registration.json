{"title": "Registration Management", "applicationRegistration": "Application Registration", "tenantRegistration": "Tenant Management", "quotaManagement": "Quota Management", "authorizationManagement": "Authorization Management", "approvalWorkflow": "Approval Workflow", "applications": {"title": "Application Registration", "description": "Manage and register applications, configure client credentials and permissions", "newApplication": "New Application", "totalApplications": "Total Applications", "activeApplications": "Active Applications", "monthlyNew": "Monthly New", "registeredApplications": "Registered Applications", "manageApplications": "Manage your application registrations and configurations", "clientId": "Client ID", "active": "Active", "inactive": "Inactive"}, "tenants": {"title": "Tenant Management", "description": "Manage multi-tenant environment, configure tenant isolation and resource allocation", "newTenant": "New Tenant", "totalTenants": "Total Tenants", "totalUsers": "Total Users", "activeTenants": "Active Tenants", "registeredTenants": "Managed Tenants", "manageTenants": "Manage your tenant configurations and resource allocation", "domain": "Domain", "userCount": "User Count", "acrossAllTenants": "Across All Tenants", "activeRate": "Active Rate"}, "quotas": {"title": "Quota Management", "description": "Manage system resource quotas, monitor usage and set limits", "newQuota": "New Quota", "totalQuotas": "Total Quotas", "warningQuotas": "Warning Quotas", "averageUsage": "Average Usage", "exceededQuotas": "Exceeded <PERSON><PERSON><PERSON>", "quotaRules": "Quota Rules", "monitorQuotas": "Monitor and manage system resource usage quotas", "apiCalls": "API Calls", "storage": "Storage", "users": "Users", "monthly": "Monthly", "used": "Used", "limit": "Limit", "normal": "Normal", "warning": "Warning", "danger": "Exceeded", "needAttention": "Need Attention", "handleImmediately": "Handle Immediately", "acrossAllQuotas": "Across All Quotas", "activeQuotaRules": "Active Quota Rules"}, "authorizations": {"title": "Authorization Management", "description": "Manage user permissions and access control, configure roles and permission assignments", "newAuthorization": "New Authorization", "permissionRules": "Permission Rules", "authorizedUsers": "Authorized Users", "apiPermissions": "API Permissions", "rolePermissions": "Role Permissions", "permissionConfig": "Permission Configuration", "managePermissions": "Manage user roles and permission assignments", "totalAuthorizedUsers": "Total Authorized Users", "apiAccessRules": "API Access Rules", "rolePermissionRules": "Role Permission Rules", "activePermissionRules": "Active Permission Rules", "role": "Role", "api": "API", "permissions": "Permissions", "read": "Read", "write": "Write", "delete": "Delete", "admin": "Admin"}, "approval": {"title": "Approval Workflow", "description": "Configure and manage approval workflows for tenant registration, application registration, and quota requests", "newWorkflow": "New Workflow", "workflowName": "Workflow Name", "workflowType": "Workflow Type", "workflowStatus": "Workflow Status", "workflowDescription": "Workflow Description", "approvalSteps": "Approval Steps", "requestCount": "Request Count", "searchWorkflows": "Search workflows...", "filterByType": "Filter by Type", "filterByStatus": "Filter by Status", "allTypes": "All Types", "allStatuses": "All Statuses", "tenantRegistration": "Tenant Registration", "applicationRegistration": "Application Registration", "quotaRequest": "Quota Request", "enabled": "Enabled", "disabled": "Disabled", "viewRequests": "View Requests", "editWorkflow": "Edit Workflow", "deleteWorkflow": "Delete Workflow", "enableWorkflow": "Enable Workflow", "disableWorkflow": "Disable Workflow", "confirmDelete": "Are you sure you want to delete this workflow?", "deleteSuccess": "Workflow deleted successfully", "deleteError": "Failed to delete workflow", "updateSuccess": "Workflow updated successfully", "updateError": "Failed to update workflow", "noWorkflows": "No workflows found", "createFirstWorkflow": "Create your first approval workflow"}, "common": {"active": "Active", "inactive": "Inactive", "status": "Status", "actions": "Actions", "settings": "Settings", "delete": "Delete", "edit": "Edit", "create": "Create", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "createdAt": "Created At", "updatedAt": "Updated At"}}