import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslation } from "next-i18next";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import { Label } from "@/src/components/ui/label";
import { ArrowLeft, Save } from "lucide-react";
import { useTenantById, useUpdateTenant } from "../hooks/useTenantManagement";
import { UpdateTenantSchema, type UpdateTenantInput } from "../schemas";

interface TenantEditProps {
  tenantId: string;
  projectId: string;
  onBack?: () => void;
  onSuccess?: () => void;
}

export const TenantEdit: React.FC<TenantEditProps> = ({
  tenantId,
  projectId,
  onBack,
  onSuccess,
}) => {
  const { t } = useTranslation(["common", "registration"]);
  const { data: tenant, isLoading: loadingTenant } = useTenantById(
    tenantId,
    projectId,
  );
  const updateMutation = useUpdateTenant();

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors, isSubmitting, isDirty },
  } = useForm<UpdateTenantInput>({
    resolver: zodResolver(UpdateTenantSchema),
  });

  // 当租户数据加载完成时，填充表单
  useEffect(() => {
    if (tenant) {
      const formData: UpdateTenantInput = {
        tenantId: tenant.id,
        projectId: projectId,
        name: tenant.name,
        displayName: tenant.displayName || "",
        description: tenant.description || "",
        type: tenant.type,
        category: tenant.category,
        contactName: tenant.contactName,
        contactEmail: tenant.contactEmail,
        contactPhone: tenant.contactPhone || "",
        address: tenant.address || "",
        website: tenant.website || "",
        licenseNumber: tenant.licenseNumber || "",
        taxId: tenant.taxId || "",
        legalPerson: tenant.legalPerson || "",
      };
      reset(formData);
    }
  }, [tenant, reset, projectId]);

  const onSubmit = async (data: UpdateTenantInput) => {
    try {
      await updateMutation.mutateAsync(data);
      // 成功后调用回调函数
      onSuccess?.();
    } catch (error) {
      console.error("更新租户失败:", error);
      // 错误处理已经在 hook 中处理了，这里不需要额外处理
    }
  };

  if (loadingTenant) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
          <p className="mt-2 text-sm text-gray-600">{t("common:loading")}</p>
        </div>
      </div>
    );
  }

  if (!tenant) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-600">{t("common:error")}: 租户不存在</p>
          {onBack && (
            <Button variant="outline" onClick={onBack} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("common:back")}
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full max-h-screen space-y-6 overflow-y-auto pb-8">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("common:back")}
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold">编辑租户</h1>
            <p className="text-gray-600">{tenant.displayName || tenant.name}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="name">租户名称 *</Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="请输入租户名称"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.name.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="displayName">显示名称</Label>
                <Input
                  id="displayName"
                  {...register("displayName")}
                  placeholder="请输入显示名称"
                />
                {errors.displayName && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.displayName.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="category">分类 *</Label>
                <Input
                  id="category"
                  {...register("category")}
                  placeholder="请输入分类"
                />
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.category.message}
                  </p>
                )}
              </div>
            </div>
            <div>
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="请输入租户描述"
                rows={3}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.description.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 联系信息 */}
        <Card>
          <CardHeader>
            <CardTitle>联系信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="contactName">联系人姓名 *</Label>
                <Input
                  id="contactName"
                  {...register("contactName")}
                  placeholder="请输入联系人姓名"
                />
                {errors.contactName && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.contactName.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="contactEmail">联系邮箱 *</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  {...register("contactEmail")}
                  placeholder="请输入联系邮箱"
                />
                {errors.contactEmail && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.contactEmail.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="contactPhone">联系电话</Label>
                <Input
                  id="contactPhone"
                  {...register("contactPhone")}
                  placeholder="请输入联系电话"
                />
                {errors.contactPhone && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.contactPhone.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="website">网站</Label>
                <Input
                  id="website"
                  type="url"
                  {...register("website")}
                  placeholder="请输入网站地址"
                />
                {errors.website && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.website.message}
                  </p>
                )}
              </div>
            </div>
            <div>
              <Label htmlFor="address">地址</Label>
              <Textarea
                id="address"
                {...register("address")}
                placeholder="请输入地址"
                rows={2}
              />
              {errors.address && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 法律信息 */}
        <Card>
          <CardHeader>
            <CardTitle>法律信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="licenseNumber">许可证号码</Label>
                <Input
                  id="licenseNumber"
                  {...register("licenseNumber")}
                  placeholder="请输入许可证号码"
                />
                {errors.licenseNumber && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.licenseNumber.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="taxId">税务ID</Label>
                <Input
                  id="taxId"
                  {...register("taxId")}
                  placeholder="请输入税务ID"
                />
                {errors.taxId && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.taxId.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="legalPerson">法人代表</Label>
                <Input
                  id="legalPerson"
                  {...register("legalPerson")}
                  placeholder="请输入法人代表"
                />
                {errors.legalPerson && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.legalPerson.message}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-4">
          {onBack && (
            <Button type="button" variant="outline" onClick={onBack}>
              {t("common:cancel")}
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting || !isDirty}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                保存中...
              </div>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {t("common:save")}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
