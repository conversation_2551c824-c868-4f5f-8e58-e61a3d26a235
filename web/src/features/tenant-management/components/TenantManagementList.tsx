import React, { useState } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import {
  TenantType,
  TenantStatus,
  TenantTypeLabels,
  TenantStatusLabels,
  TenantStatusColors,
  type Tenant,
} from "../types";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/src/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import {
  Loader2,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Plus,
  Check,
  X,
  Pause,
  RefreshCw,
  Building2,
} from "lucide-react";
import { Checkbox } from "@/src/components/ui/checkbox";

import {
  useTenantList,
  useUpdateTenantStatus,
  useDeleteTenant,
  useTenantErrorHandler,
} from "../hooks/useTenantManagement";
import { useBatchApproveTenants } from "../hooks/useTenantApproval";

interface TenantManagementListProps {
  onViewTenant?: (tenantId: string) => void;
  onEditTenant?: (tenantId: string) => void;
  onCreateTenant?: () => void;
}

// 状态图标映射
const statusIcons: Record<TenantStatus, React.ReactNode> = {
  [TenantStatus.PENDING]: <Clock className="h-3 w-3" />,
  [TenantStatus.ACTIVE]: <CheckCircle className="h-3 w-3" />,
  [TenantStatus.INACTIVE]: <XCircle className="h-3 w-3" />,
  [TenantStatus.SUSPENDED]: <AlertCircle className="h-3 w-3" />,
  [TenantStatus.REJECTED]: <XCircle className="h-3 w-3" />,
  [TenantStatus.EXPIRED]: <AlertCircle className="h-3 w-3" />,
};

export const TenantManagementList: React.FC<TenantManagementListProps> = ({
  onViewTenant,
  onEditTenant,
  onCreateTenant,
}) => {
  const { t } = useTranslation(["common", "registration"]);
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { data: session } = useSession();

  // 筛选和搜索状态
  const [filters, setFilters] = useState({
    status: undefined as TenantStatus | undefined,
    type: undefined as TenantType | undefined,
    search: "",
    page: 0,
    limit: 20,
  });

  // 批量选择状态
  const [selectedTenants, setSelectedTenants] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // 获取租户列表
  const {
    data: tenantData,
    isLoading,
    error,
    refetch,
  } = useTenantList({
    projectId,
    ...filters,
  });

  const updateTenantStatus = useUpdateTenantStatus();
  const deleteTenant = useDeleteTenant();
  const { handleError } = useTenantErrorHandler();
  const batchApproveTenants = useBatchApproveTenants();

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, search: value, page: 0 }));
  };

  // 处理状态筛选
  const handleStatusFilter = (status: string) => {
    setFilters((prev) => ({
      ...prev,
      status: status === "all" ? undefined : (status as TenantStatus),
      page: 0,
    }));
  };

  // 处理类型筛选
  const handleTypeFilter = (type: string) => {
    setFilters((prev) => ({
      ...prev,
      type: type === "all" ? undefined : (type as TenantType),
      page: 0,
    }));
  };

  // 处理状态更新
  const handleStatusUpdate = async (tenantId: string, status: TenantStatus) => {
    try {
      await updateTenantStatus.mutateAsync({
        tenantId,
        status,
        projectId,
      });
    } catch (error) {
      handleError(error, "更新租户状态失败");
    }
  };

  // 处理删除
  const handleDelete = async (tenantId: string) => {
    if (!confirm("确定要删除这个租户吗？此操作不可撤销。")) {
      return;
    }

    try {
      await deleteTenant.mutateAsync({ tenantId, projectId });
    } catch (error) {
      handleError(error, "删除租户失败");
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedTenants(tenantData?.tenants?.map((tenant) => tenant.id) || []);
    } else {
      setSelectedTenants([]);
    }
  };

  // 处理单选
  const handleSelectTenant = (tenantId: string, checked: boolean) => {
    if (checked) {
      setSelectedTenants((prev) => [...prev, tenantId]);
    } else {
      setSelectedTenants((prev) => prev.filter((id) => id !== tenantId));
      setSelectAll(false);
    }
  };

  // 单个状态更新（参考应用注册优化逻辑，使用批量接口实现单个更新）
  const handleSingleStatusUpdate = async (tenantId: string, status: string) => {
    try {
      await batchApproveTenants.mutateAsync({
        projectId,
        tenantIds: [tenantId], // 单个租户作为数组传递
        approved: status === "ACTIVE",
        status: status.toLowerCase() as any, // 传递具体状态
        reason: undefined, // 快速操作不需要原因
      });
    } catch (error) {
      handleError(error, "更新租户状态失败");
    }
  };

  // 批量状态更新 - 参考应用注册优化逻辑
  const handleBatchStatusUpdate = async (status: string) => {
    if (selectedTenants.length === 0) return;

    const statusLabels: Record<string, string> = {
      ACTIVE: "激活",
      REJECTED: "拒绝",
      SUSPENDED: "暂停",
    };

    if (
      confirm(
        `确定要将选中的 ${selectedTenants.length} 个租户设置为${statusLabels[status]}状态吗？`,
      )
    ) {
      try {
        // 使用批量审批接口，现在支持任意状态切换
        await batchApproveTenants.mutateAsync({
          projectId,
          tenantIds: selectedTenants,
          approved: status === "ACTIVE",
          status: status.toLowerCase() as any, // 传递具体状态
          reason: undefined,
        });
        setSelectedTenants([]);
        setSelectAll(false);
      } catch (error) {
        // 错误已在hook中处理
      }
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-600">加载租户列表失败</p>
            <Button onClick={() => refetch()} className="mt-2">
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const tenants = tenantData?.tenants || [];
  const totalCount = tenantData?.totalCount || 0;

  // 获取状态图标
  const getStatusIcon = (status: TenantStatus) => {
    switch (status) {
      case TenantStatus.PENDING:
        return <Clock className="h-4 w-4 text-blue-500" />;
      case TenantStatus.ACTIVE:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case TenantStatus.REJECTED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      case TenantStatus.SUSPENDED:
        return <Pause className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取状态Badge样式
  const getStatusBadgeClass = (status: TenantStatus) => {
    switch (status) {
      case TenantStatus.PENDING:
        return "border-blue-200 bg-blue-50 text-blue-700";
      case TenantStatus.ACTIVE:
        return "border-green-200 bg-green-50 text-green-700";
      case TenantStatus.REJECTED:
        return "border-red-200 bg-red-50 text-red-700";
      case TenantStatus.SUSPENDED:
        return "border-orange-200 bg-orange-50 text-orange-700";
      default:
        return "border-gray-200 bg-gray-50 text-gray-700";
    }
  };

  return (
    <div className="min-h-screen space-y-6 overflow-y-auto pb-8">
      {/* 筛选和搜索 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardDescription>管理和监控所有租户</CardDescription>
            </div>
            <Button onClick={onCreateTenant}>
              <Plus className="mr-2 h-4 w-4" />
              新建租户
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="搜索租户名称、联系人..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select
              value={filters.status || "all"}
              onValueChange={handleStatusFilter}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="筛选状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                {Object.entries(TenantStatusLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={filters.type || "all"}
              onValueChange={handleTypeFilter}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="筛选类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                {Object.entries(TenantTypeLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作提示 */}
      {selectedTenants.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="py-4">
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">
                  已选择 {selectedTenants.length} 个租户
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedTenants([]);
                    setSelectAll(false);
                  }}
                  className="h-8"
                >
                  取消选择
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusUpdate("ACTIVE")}
                  className="h-8 text-green-600 hover:bg-green-50 hover:text-green-700"
                >
                  <Check className="mr-1 h-3 w-3" />
                  激活
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusUpdate("REJECTED")}
                  className="h-8 text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <X className="mr-1 h-3 w-3" />
                  拒绝
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusUpdate("SUSPENDED")}
                  className="h-8 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                >
                  <Pause className="mr-1 h-3 w-3" />
                  暂停
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 租户列表 */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : tenants.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-500">暂无租户数据</p>
              {onCreateTenant && (
                <Button onClick={onCreateTenant} className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  创建第一个租户
                </Button>
              )}
            </div>
          ) : (
            <div
              className="overflow-auto rounded-md border bg-background"
              style={{ maxHeight: "calc(100vh - 400px)", minHeight: "400px" }}
            >
              <div className="overflow-x-auto">
                <Table className="min-w-[1200px]">
                  <TableHeader className="sticky top-0 z-10 border-b bg-background">
                    <TableRow>
                      <TableHead className="w-12 min-w-12">
                        <Checkbox
                          checked={selectAll}
                          onCheckedChange={handleSelectAll}
                          aria-label="全选"
                        />
                      </TableHead>
                      <TableHead className="w-16 min-w-16 text-center">
                        序号
                      </TableHead>
                      <TableHead className="w-52 min-w-52">租户信息</TableHead>
                      <TableHead className="hidden w-28 min-w-28 sm:table-cell">
                        类型
                      </TableHead>
                      <TableHead className="hidden w-48 min-w-48 md:table-cell">
                        联系人
                      </TableHead>
                      <TableHead className="w-32 min-w-32">状态</TableHead>
                      <TableHead className="hidden w-24 min-w-24 lg:table-cell">
                        创建时间
                      </TableHead>
                      <TableHead className="w-36 min-w-36">操作员</TableHead>
                      <TableHead className="w-40 min-w-40">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tenants.map((tenant, index) => {
                      const isSelected = selectedTenants.includes(tenant.id);
                      const serialNumber =
                        filters.page * filters.limit + index + 1;

                      return (
                        <TableRow
                          key={tenant.id}
                          className={isSelected ? "bg-muted/50" : ""}
                        >
                          {/* 多选复选框 */}
                          <TableCell>
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={(checked) =>
                                handleSelectTenant(
                                  tenant.id,
                                  checked as boolean,
                                )
                              }
                              aria-label={`选择租户 ${tenant.displayName || tenant.name}`}
                            />
                          </TableCell>

                          {/* 序号 */}
                          <TableCell className="text-center text-sm text-muted-foreground">
                            {serialNumber}
                          </TableCell>
                          {/* 租户信息 */}
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Building2 className="h-4 w-4 text-blue-500" />
                              <div>
                                <div className="font-medium">
                                  {tenant.displayName || tenant.name}
                                </div>
                                {tenant.description && (
                                  <div className="text-xs text-muted-foreground">
                                    {tenant.description}
                                  </div>
                                )}
                                {/* 在小屏幕上显示额外信息 */}
                                <div className="mt-1 flex flex-col gap-1 text-xs text-muted-foreground sm:hidden">
                                  <div className="flex items-center gap-2">
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {TenantTypeLabels[tenant.type]}
                                    </Badge>
                                    <span>
                                      {new Date(
                                        tenant.createdAt,
                                      ).toLocaleDateString()}
                                    </span>
                                  </div>
                                  {tenant.contactName && (
                                    <div>
                                      {tenant.contactName} •{" "}
                                      {tenant.contactEmail}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <Badge variant="outline">
                              {TenantTypeLabels[tenant.type]}
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            <div>
                              <div className="font-medium">
                                {tenant.contactName}
                              </div>
                              <div className="text-sm text-gray-500">
                                {tenant.contactEmail}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(tenant.status)}
                              <Badge
                                variant="outline"
                                className={getStatusBadgeClass(tenant.status)}
                              >
                                {TenantStatusLabels[tenant.status]}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            {new Date(tenant.createdAt).toLocaleDateString()}
                          </TableCell>

                          {/* 操作员信息 */}
                          <TableCell>
                            <div className="text-sm">
                              <div className="font-medium">
                                {session?.user?.name ||
                                  session?.user?.email ||
                                  "未知用户"}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(tenant.createdAt).toLocaleDateString(
                                  "zh-CN",
                                )}
                              </div>
                            </div>
                          </TableCell>

                          {/* 操作按钮 */}
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {/* 快速状态切换按钮 - 参考应用注册优化逻辑，所有状态可互相切换 */}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  handleSingleStatusUpdate(tenant.id, "ACTIVE")
                                }
                                className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700"
                                title="激活"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  handleSingleStatusUpdate(
                                    tenant.id,
                                    "REJECTED",
                                  )
                                }
                                className="h-7 w-7 p-0 text-red-600 hover:bg-red-50 hover:text-red-700"
                                title="拒绝"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  handleSingleStatusUpdate(
                                    tenant.id,
                                    "SUSPENDED",
                                  )
                                }
                                className="h-7 w-7 p-0 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                                title="暂停"
                              >
                                <Pause className="h-3 w-3" />
                              </Button>

                              {/* 更多操作菜单 */}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => onViewTenant?.(tenant.id)}
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    查看详情
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => onEditTenant?.(tenant.id)}
                                  >
                                    <Edit className="mr-2 h-4 w-4" />
                                    编辑
                                  </DropdownMenuItem>
                                  {tenant.status === TenantStatus.PENDING && (
                                    <>
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleStatusUpdate(
                                            tenant.id,
                                            TenantStatus.ACTIVE,
                                          )
                                        }
                                      >
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        激活
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleStatusUpdate(
                                            tenant.id,
                                            TenantStatus.REJECTED,
                                          )
                                        }
                                      >
                                        <XCircle className="mr-2 h-4 w-4" />
                                        拒绝
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                  {tenant.status === TenantStatus.ACTIVE && (
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleStatusUpdate(
                                          tenant.id,
                                          TenantStatus.SUSPENDED,
                                        )
                                      }
                                    >
                                      <AlertCircle className="mr-2 h-4 w-4" />
                                      暂停
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem
                                    onClick={() => handleDelete(tenant.id)}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    删除
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 分页 */}
      {totalCount > filters.limit && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-500">
            共 {totalCount} 个租户，当前显示第{" "}
            {filters.page * filters.limit + 1} -{" "}
            {Math.min((filters.page + 1) * filters.limit, totalCount)} 个
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={filters.page === 0}
              onClick={() =>
                setFilters((prev) => ({ ...prev, page: prev.page - 1 }))
              }
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={(filters.page + 1) * filters.limit >= totalCount}
              onClick={() =>
                setFilters((prev) => ({ ...prev, page: prev.page + 1 }))
              }
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
