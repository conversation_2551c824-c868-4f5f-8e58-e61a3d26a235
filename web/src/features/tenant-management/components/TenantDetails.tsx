import React from "react";
import { useTranslation } from "next-i18next";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { Separator } from "@/src/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
} from "lucide-react";
import { useTenantById } from "../hooks/useTenantManagement";
import { TenantStatus } from "../types";

interface TenantDetailsProps {
  tenantId: string;
  projectId: string;
  onBack?: () => void;
  onEdit?: () => void;
}

const statusIcons = {
  [TenantStatus.PENDING]: <Clock className="h-4 w-4" />,
  [TenantStatus.ACTIVE]: <CheckCircle className="h-4 w-4" />,
  [TenantStatus.INACTIVE]: <XCircle className="h-4 w-4" />,
  [TenantStatus.SUSPENDED]: <AlertCircle className="h-4 w-4" />,
  [TenantStatus.REJECTED]: <XCircle className="h-4 w-4" />,
};

const statusLabels = {
  [TenantStatus.PENDING]: "待审核",
  [TenantStatus.ACTIVE]: "活跃",
  [TenantStatus.INACTIVE]: "非活跃",
  [TenantStatus.SUSPENDED]: "已暂停",
  [TenantStatus.REJECTED]: "已拒绝",
};

const statusColors = {
  [TenantStatus.PENDING]: "bg-yellow-100 text-yellow-800",
  [TenantStatus.ACTIVE]: "bg-green-100 text-green-800",
  [TenantStatus.INACTIVE]: "bg-gray-100 text-gray-800",
  [TenantStatus.SUSPENDED]: "bg-red-100 text-red-800",
  [TenantStatus.REJECTED]: "bg-red-100 text-red-800",
};

export const TenantDetails: React.FC<TenantDetailsProps> = ({
  tenantId,
  projectId,
  onBack,
  onEdit,
}) => {
  const { t } = useTranslation(["common", "registration"]);
  const { data: tenant, isLoading, error } = useTenantById(tenantId, projectId);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
          <p className="mt-2 text-sm text-gray-600">{t("common:loading")}</p>
        </div>
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-600">
            {t("common:error")}: 租户不存在或加载失败
          </p>
          {onBack && (
            <Button variant="outline" onClick={onBack} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("common:back")}
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full max-h-screen space-y-6 overflow-y-auto pb-8">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("common:back")}
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold">
              {tenant.displayName || tenant.name}
            </h1>
            <p className="text-gray-600">{tenant.description}</p>
          </div>
        </div>
        {onEdit && (
          <Button onClick={onEdit}>
            <Edit className="mr-2 h-4 w-4" />
            {t("common:edit")}
          </Button>
        )}
      </div>

      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle>基本信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">
                租户名称
              </label>
              <p className="mt-1">{tenant.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                显示名称
              </label>
              <p className="mt-1">{tenant.displayName || "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                租户类型
              </label>
              <p className="mt-1">{tenant.type}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">分类</label>
              <p className="mt-1">{tenant.category}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">状态</label>
              <div className="mt-1">
                <Badge
                  className={
                    statusColors[tenant.status as keyof typeof statusColors]
                  }
                >
                  {statusIcons[tenant.status as keyof typeof statusIcons]}
                  <span className="ml-1">
                    {statusLabels[tenant.status as keyof typeof statusLabels]}
                  </span>
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                创建时间
              </label>
              <p className="mt-1">
                {new Date(tenant.createdAt).toLocaleString()}
              </p>
            </div>
          </div>
          {tenant.description && (
            <div>
              <label className="text-sm font-medium text-gray-500">描述</label>
              <p className="mt-1">{tenant.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 联系信息 */}
      <Card>
        <CardHeader>
          <CardTitle>联系信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">
                联系人姓名
              </label>
              <p className="mt-1">{tenant.contactName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                联系邮箱
              </label>
              <p className="mt-1">{tenant.contactEmail}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                联系电话
              </label>
              <p className="mt-1">{tenant.contactPhone || "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">地址</label>
              <p className="mt-1">{tenant.address || "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">网站</label>
              <p className="mt-1">
                {tenant.website ? (
                  <a
                    href={tenant.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {tenant.website}
                  </a>
                ) : (
                  "-"
                )}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 法律信息 */}
      <Card>
        <CardHeader>
          <CardTitle>法律信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">
                许可证号码
              </label>
              <p className="mt-1">{tenant.licenseNumber || "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                税务ID
              </label>
              <p className="mt-1">{tenant.taxId || "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                法人代表
              </label>
              <p className="mt-1">{tenant.legalPerson || "-"}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
