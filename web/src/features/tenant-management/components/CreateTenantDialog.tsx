import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod/v4";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { type TenantRegistrationFormData } from "../types";
import { useCreateTenant } from "../hooks/useTenantManagement";
import { Button } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { Loader2, Building2 } from "lucide-react";

// 表单验证模式
const createTenantSchema = z.object({
  name: z.string().min(1, "租户名称不能为空"),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: z.enum(
    [
      "hospital_tertiary",
      "hospital_secondary",
      "hospital_primary",
      "hospital_specialized",
      "clinic",
      "health_center",
      "medical_group",
      "other",
    ],
    {
      required_error: "请选择租户类型",
    },
  ),
  category: z.string().min(1, "类型不能为空"),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url("请输入有效的网址").optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
});

type CreateTenantFormData = z.infer<typeof createTenantSchema>;

interface CreateTenantDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: (tenant: any) => void;
}

// 租户类型选项（与服务器端保持一致）
const tenantTypeOptions = [
  { value: "hospital_tertiary", label: "三甲医院" },
  { value: "hospital_secondary", label: "二甲医院" },
  { value: "hospital_primary", label: "一甲医院" },
  { value: "hospital_specialized", label: "专科医院" },
  { value: "clinic", label: "诊所" },
  { value: "health_center", label: "卫生院" },
  { value: "medical_group", label: "医疗集团" },
  { value: "other", label: "其他" },
];

// 医院类型选项
const categoryOptions = [
  "综合医院",
  "专科医院",
  "中医医院",
  "妇幼保健院",
  "精神病医院",
  "传染病医院",
  "肿瘤医院",
  "心血管医院",
  "眼科医院",
  "口腔医院",
  "康复医院",
  "护理院",
  "社区卫生服务中心",
  "乡镇卫生院",
  "村卫生室",
  "门诊部",
  "诊所",
  "医务室",
  "其他",
];

export function CreateTenantDialog({
  open,
  onOpenChange,
  onSuccess,
}: CreateTenantDialogProps) {
  const { t } = useTranslation(["common", "registration", "validation"]);
  const router = useRouter();
  const projectId = router.query.projectId as string;

  // 添加步骤状态
  const [step, setStep] = useState(1);
  const [stepError, setStepError] = useState<string | null>(null);

  const createTenant = useCreateTenant();

  const form = useForm<CreateTenantFormData>({
    resolver: zodResolver(createTenantSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      displayName: "",
      description: "",
      type: undefined,
      category: "",
      contactName: "",
      contactEmail: "",
      contactPhone: "",
      address: "",
      website: "",
      licenseNumber: "",
      taxId: "",
      legalPerson: "",
    },
  });

  // 步骤导航功能
  const nextStep = async () => {
    setStepError(null);

    // 验证当前步骤的必填字段
    let fieldsToValidate: (keyof CreateTenantFormData)[] = [];

    if (step === 1) {
      fieldsToValidate = ["name", "type", "category"];
    } else if (step === 2) {
      fieldsToValidate = ["contactName", "contactEmail"];
    }

    // 使用 react-hook-form 的 trigger 方法验证字段
    const isValid = await form.trigger(fieldsToValidate);

    if (isValid && step < 3) {
      setStep(step + 1);
    } else if (!isValid) {
      setStepError("请填写所有必填字段");
    }
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
    setStepError(null);
  };

  const onSubmit = async (data: CreateTenantFormData) => {
    try {
      const tenant = await createTenant.mutateAsync({
        projectId,
        ...data,
      });

      form.reset();
      setStep(1);
      setStepError(null);
      onOpenChange(false);
      onSuccess?.(tenant);
    } catch (error) {
      // 错误处理已在 hook 中完成
      console.error("创建租户失败:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[90vh] max-w-3xl flex-col p-6">
        <DialogHeader className="flex-shrink-0 pb-6">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <Building2 className="h-6 w-6" />
            新建租户 - 第 {step} 步，共 3 步
          </DialogTitle>
          <DialogDescription className="mt-3 text-sm text-muted-foreground">
            {step === 1 && "填写租户的基本信息"}
            {step === 2 && "填写联系人信息"}
            {step === 3 && "填写其他补充信息"}
          </DialogDescription>

          {/* 步骤指示器 */}
          <div className="mt-4 flex items-center justify-center space-x-4">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                    stepNumber === step
                      ? "bg-primary text-primary-foreground"
                      : stepNumber < step
                        ? "bg-primary/20 text-primary"
                        : "bg-muted text-muted-foreground"
                  }`}
                >
                  {stepNumber}
                </div>
                {stepNumber < 3 && (
                  <div
                    className={`h-0.5 w-12 ${
                      stepNumber < step ? "bg-primary" : "bg-muted"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          {/* 错误提示 */}
          {stepError && (
            <div className="mt-4 rounded-md bg-destructive/15 p-3">
              <p className="text-sm text-destructive">{stepError}</p>
            </div>
          )}
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex h-full flex-col"
          >
            <div className="flex-1 space-y-8 overflow-y-auto pb-4 pr-2">
              {/* 第一步：基本信息 */}
              {step === 1 && (
                <div className="space-y-6">
                  <h3 className="border-b border-border pb-3 text-lg font-semibold text-foreground">
                    基本信息
                  </h3>

                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            租户名称 *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入租户名称"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="displayName"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            显示名称
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入显示名称"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            租户类型 *
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="h-10">
                                <SelectValue placeholder="请选择租户类型" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {tenantTypeOptions.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            具体类型 *
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="h-10">
                                <SelectValue placeholder="请选择具体类型" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {categoryOptions.map((category) => (
                                <SelectItem key={category} value={category}>
                                  {category}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-medium">
                          描述
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请输入租户描述"
                            className="min-h-[80px] resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* 第二步：联系信息 */}
              {step === 2 && (
                <div className="space-y-6">
                  <h3 className="border-b border-border pb-3 text-lg font-semibold text-foreground">
                    联系信息
                  </h3>

                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="contactName"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            联系人姓名 *
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入联系人姓名"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contactEmail"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            联系人邮箱 *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="请输入联系人邮箱"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contactPhone"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            联系电话
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入联系电话"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="website"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            网站
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://example.com"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-medium">
                          地址
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请输入地址"
                            className="min-h-[80px] resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* 第三步：其他信息 */}
              {step === 3 && (
                <div className="space-y-6">
                  <h3 className="border-b border-border pb-3 text-lg font-semibold text-foreground">
                    其他信息
                  </h3>
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="licenseNumber"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            许可证号
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入许可证号"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="taxId"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            税务登记号
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入税务登记号"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="legalPerson"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-sm font-medium">
                            法人代表
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入法人代表"
                              className="h-10"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}
            </div>

            <DialogFooter className="mt-8 flex-shrink-0 gap-3 border-t pt-6">
              <div className="flex w-full justify-between">
                <div>
                  {step > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={prevStep}
                      disabled={createTenant.isPending}
                      className="h-10 px-6"
                    >
                      上一步
                    </Button>
                  )}
                </div>

                <div className="flex gap-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                    disabled={createTenant.isPending}
                    className="h-10 px-6"
                  >
                    取消
                  </Button>

                  {step < 3 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      disabled={createTenant.isPending}
                      className="h-10 px-6"
                    >
                      下一步
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={createTenant.isPending}
                      className="h-10 px-6"
                    >
                      {createTenant.isPending && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      创建租户
                    </Button>
                  )}
                </div>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
