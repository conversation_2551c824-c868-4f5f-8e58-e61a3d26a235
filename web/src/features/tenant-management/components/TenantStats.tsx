import React from "react";
import { Users, UserCheck, Clock, UserX } from "lucide-react";
import { StatsCard, StatsGrid } from "@/src/components/ui/stats-card";
import { api } from "@/src/utils/api";

interface TenantStatsProps {
  projectId: string;
}

export function TenantStats({ projectId }: TenantStatsProps) {
  const { data: stats, isLoading } = api.tenantManagement.tenant.stats.useQuery(
    { projectId },
    {
      refetchInterval: 30000, // 每30秒刷新一次
    }
  );

  return (
    <StatsGrid className="mb-6">
      <StatsCard
        title="总租户数"
        value={stats?.totalCount ?? 0}
        description="系统中的所有租户"
        icon={Users}
        iconClassName="text-blue-500"
        loading={isLoading}
      />
      <StatsCard
        title="活跃租户"
        value={stats?.activeCount ?? 0}
        description={`活跃率 ${stats?.activeRate ?? 0}%`}
        icon={UserCheck}
        iconClassName="text-green-500"
        trend={{
          value: stats?.activeRate ?? 0,
          label: "活跃率",
          isPositive: (stats?.activeRate ?? 0) >= 70,
        }}
        loading={isLoading}
      />
      <StatsCard
        title="待审核"
        value={stats?.pendingCount ?? 0}
        description="等待审核的租户"
        icon={Clock}
        iconClassName="text-orange-500"
        loading={isLoading}
      />
      <StatsCard
        title="已暂停"
        value={stats?.suspendedCount ?? 0}
        description="暂停使用的租户"
        icon={UserX}
        iconClassName="text-red-500"
        loading={isLoading}
      />
    </StatsGrid>
  );
}
