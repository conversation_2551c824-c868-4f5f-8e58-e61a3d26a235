import { z } from "zod/v4";

// 租户类型和状态枚举
export const TenantType = z.enum([
  "hospital_tertiary",
  "hospital_secondary",
  "hospital_primary",
  "hospital_specialized",
  "clinic",
  "health_center",
  "medical_group",
  "other",
]);

export const TenantStatus = z.enum([
  "pending",
  "active",
  "inactive",
  "suspended",
  "rejected",
  "expired",
]);

// 创建租户的输入验证模式
export const CreateTenantSchema = z.object({
  projectId: z.string(),
  name: z.string().min(1, "租户名称不能为空"),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: TenantType,
  category: z.string().min(1, "类型不能为空"),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
  settings: z.record(z.string(), z.any()).optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

// 更新租户的输入验证模式
export const UpdateTenantSchema = z.object({
  tenantId: z.string(),
  projectId: z.string(),
  name: z.string().min(1, "租户名称不能为空").optional(),
  displayName: z.string().optional(),
  description: z.string().optional(),
  type: TenantType.optional(),
  category: z.string().min(1, "类型不能为空").optional(),
  contactName: z.string().min(1, "联系人姓名不能为空").optional(),
  contactEmail: z.string().email("请输入有效的邮箱地址").optional(),
  contactPhone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  licenseNumber: z.string().optional(),
  taxId: z.string().optional(),
  legalPerson: z.string().optional(),
  settings: z.record(z.string(), z.any()).optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

// 租户过滤的输入验证模式
export const TenantFilterSchema = z.object({
  projectId: z.string(),
  status: TenantStatus.optional(),
  type: TenantType.optional(),
  category: z.string().optional(),
  search: z.string().optional(),
  page: z.number().min(0).default(0),
  limit: z.number().min(1).max(100).default(20),
});

// 类型推断
export type TenantTypeValue = z.infer<typeof TenantType>;
export type TenantStatusValue = z.infer<typeof TenantStatus>;
export type CreateTenantInput = z.infer<typeof CreateTenantSchema>;
export type UpdateTenantInput = z.infer<typeof UpdateTenantSchema>;
export type TenantFilterInput = z.infer<typeof TenantFilterSchema>;

// 租户类型选项
export const TENANT_TYPE_OPTIONS = [
  { value: "hospital_tertiary", label: "三级医院" },
  { value: "hospital_secondary", label: "二级医院" },
  { value: "hospital_primary", label: "一级医院" },
  { value: "hospital_specialized", label: "专科医院" },
  { value: "clinic", label: "诊所" },
  { value: "health_center", label: "卫生中心" },
  { value: "medical_group", label: "医疗集团" },
  { value: "other", label: "其他" },
] as const;

// 租户状态选项
export const TENANT_STATUS_OPTIONS = [
  { value: "pending", label: "待审核" },
  { value: "active", label: "活跃" },
  { value: "inactive", label: "非活跃" },
  { value: "suspended", label: "已暂停" },
  { value: "rejected", label: "已拒绝" },
  { value: "expired", label: "已过期" },
] as const;
