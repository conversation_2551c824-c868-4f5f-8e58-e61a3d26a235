import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { type TenantListParams } from "../types";

// 租户管理相关的 React Query hooks

// 获取租户统计信息
export const useTenantStats = (projectId: string) => {
  return api.tenantManagement.tenant.stats.useQuery(
    { projectId },
    {
      refetchInterval: 30000, // 每30秒刷新一次
    },
  );
};

// 创建租户
export const useCreateTenant = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.create.useMutation({
    onSuccess: () => {
      toast.success("租户创建成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });
    },
    onError: (error: any) => {
      console.error("创建租户失败:", error);
      const message = error?.message || "创建租户失败，请重试";
      toast.error(message);
    },
  });
};

// 获取租户列表
export const useTenantList = (
  params: TenantListParams & { projectId: string },
) => {
  return api.tenantManagement.tenant.list.useQuery({
    ...params,
    page: params.page || 0,
    limit: params.limit || 20,
  });
};

// 获取租户详情
export const useTenant = (tenantId: string, projectId: string) => {
  return api.tenantManagement.tenant.byId.useQuery(
    { tenantId, projectId },
    { enabled: !!tenantId && !!projectId },
  );
};

// 获取租户详情的别名
export const useTenantById = useTenant;

// 更新租户信息
export const useUpdateTenant = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.update.useMutation({
    onSuccess: () => {
      toast.success("租户信息更新成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });

      // 刷新租户详情
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "byId"]],
      });
    },
    onError: (error: any) => {
      console.error("更新租户信息失败:", error);
      const message = error?.message || "更新租户信息失败，请重试";
      toast.error(message);
    },
  });
};

// 更新租户状态
export const useUpdateTenantStatus = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.updateStatus.useMutation({
    onSuccess: () => {
      toast.success("租户状态更新成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });

      // 刷新租户详情
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "byId"]],
      });
    },
    onError: (error: any) => {
      console.error("更新租户状态失败:", error);
      const message = error?.message || "更新租户状态失败，请重试";
      toast.error(message);
    },
  });
};

// 删除租户
export const useDeleteTenant = () => {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.delete.useMutation({
    onSuccess: () => {
      toast.success("租户删除成功");

      // 刷新租户列表
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });
    },
    onError: (error: any) => {
      console.error("删除租户失败:", error);
      const message = error?.message || "删除租户失败，请重试";
      toast.error(message);
    },
  });
};

// 错误处理 hook
export const useTenantErrorHandler = () => {
  const handleError = (error: any, defaultMessage = "操作失败") => {
    console.error("租户管理错误:", error);

    let message = defaultMessage;

    if (error?.message) {
      message = error.message;
    } else if (error?.data?.message) {
      message = error.data.message;
    } else if (typeof error === "string") {
      message = error;
    }

    toast.error(message);
  };

  const getErrorMessage = (error: any, defaultMessage = "操作失败") => {
    if (error?.message) {
      return error.message;
    } else if (error?.data?.message) {
      return error.data.message;
    } else if (typeof error === "string") {
      return error;
    }
    return defaultMessage;
  };

  return {
    handleError,
    getErrorMessage,
  };
};
