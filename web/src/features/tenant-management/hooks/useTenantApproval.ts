import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// 租户状态枚举
export enum TenantStatus {
  PENDING = "pending",
  ACTIVE = "active",
  REJECTED = "rejected",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
}

// 租户类型枚举
export enum TenantType {
  INDIVIDUAL = "individual",
  ENTERPRISE = "enterprise",
  ORGANIZATION = "organization",
  GOVERNMENT = "government",
}

// 租户状态选项
export const TENANT_STATUS_OPTIONS = [
  { value: TenantStatus.PENDING, label: "待审批", color: "blue" },
  { value: TenantStatus.ACTIVE, label: "已激活", color: "green" },
  { value: TenantStatus.REJECTED, label: "已拒绝", color: "red" },
  { value: TenantStatus.INACTIVE, label: "未激活", color: "gray" },
  { value: TenantStatus.SUSPENDED, label: "已暂停", color: "yellow" },
];

// 租户类型选项
export const TENANT_TYPE_OPTIONS = [
  { value: TenantType.INDIVIDUAL, label: "个人", icon: "👤" },
  { value: TenantType.ENTERPRISE, label: "企业", icon: "🏢" },
  { value: TenantType.ORGANIZATION, label: "组织", icon: "🏛️" },
  { value: TenantType.GOVERNMENT, label: "政府", icon: "🏛️" },
];

// 获取租户状态颜色
export function getTenantStatusColor(status: TenantStatus): string {
  const option = TENANT_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.color || "gray";
}

// 获取租户状态标签
export function getTenantStatusLabel(status: TenantStatus): string {
  const option = TENANT_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.label || status;
}

// 获取租户类型标签
export function getTenantTypeLabel(type: TenantType): string {
  const option = TENANT_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.label || type;
}

// 获取租户类型图标
export function getTenantTypeIcon(type: TenantType): string {
  const option = TENANT_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.icon || "🏢";
}

// 使用租户列表
export function useTenantList(
  projectId: string,
  filters?: {
    status?: TenantStatus;
    type?: TenantType;
    category?: string;
    search?: string;
  },
) {
  return api.tenant.list.useQuery(
    {
      projectId,
      ...filters,
    },
    {
      enabled: !!projectId,
      refetchOnWindowFocus: false,
    },
  );
}

// 使用租户统计
export function useTenantStats(projectId: string) {
  return api.tenant.stats.useQuery(
    { projectId },
    {
      enabled: !!projectId,
      refetchOnWindowFocus: false,
    },
  );
}

// 使用批量租户审批
export function useBatchApproveTenants() {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      toast.success(variables.approved ? "批量审批通过" : "批量审批拒绝", {
        description: `已${variables.approved ? "激活" : "拒绝"} ${data.processedCount} 个租户`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("批量操作失败", {
        description: error.message,
      });
    },
  });
}

// 使用创建租户
export function useCreateTenant() {
  const queryClient = useQueryClient();

  return api.tenant.create.useMutation({
    onSuccess: (data) => {
      toast.success("租户创建成功", {
        description: `租户 "${data.name}" 已创建，等待审批`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["tenant", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["tenant", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("创建租户失败", {
        description: error.message,
      });
    },
  });
}

// 使用更新租户
export function useUpdateTenant() {
  const queryClient = useQueryClient();

  return api.tenant.update.useMutation({
    onSuccess: (data) => {
      toast.success("租户更新成功", {
        description: `租户 "${data.name}" 已更新`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["tenant", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["tenant", "byId"]],
      });
    },
    onError: (error) => {
      toast.error("更新租户失败", {
        description: error.message,
      });
    },
  });
}

// 使用删除租户
export function useDeleteTenant() {
  const queryClient = useQueryClient();

  return api.tenant.delete.useMutation({
    onSuccess: () => {
      toast.success("租户删除成功");

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["tenant", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["tenant", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("删除租户失败", {
        description: error.message,
      });
    },
  });
}

// 使用单个租户审批（通过批量接口实现）
export function useApproveTenant() {
  const queryClient = useQueryClient();

  return api.tenantManagement.tenant.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      toast.success(variables.approved ? "租户审批通过" : "租户审批拒绝", {
        description: `租户已${variables.approved ? "激活" : "拒绝"}`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["tenantManagement", "tenant", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("操作失败", {
        description: error.message,
      });
    },
  });
}
