import {
  createTRPCRouter,
  protectedProjectProcedure,
} from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { throwIfNoProjectAccess } from "@/src/features/rbac/utils/checkProjectAccess";
import { TRPCError } from "@trpc/server";
import { auditLog } from "@/src/features/audit-logs/auditLog";

// 配额类型枚举
const QuotaType = z.enum([
  "API_CALLS",
  "STORAGE",
  "USERS",
  "REQUESTS",
  "BANDWIDTH",
  "COMPUTE_TIME",
]);
const QuotaPeriod = z.enum(["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]);
const LifecyclePeriod = z.enum([
  "ONE_MONTH",
  "SIX_MONTHS",
  "ONE_YEAR",
  "NEVER_EXPIRE",
]);
const QuotaStatus = z.enum([
  "PENDING",
  "APPROVED",
  "REJECTED",
  "NORMAL",
  "WARNING",
  "EXCEEDED",
  "SUSPENDED",
]);
const ResourceType = z.enum(["TENANT", "APPLICATION", "API"]);

// 输入验证模式 - 资源使用者固定为租户，但支持选择应用和API
const CreateQuotaAllocationSchema = z
  .object({
    projectId: z.string(),
    resourceType: ResourceType,
    tenantId: z.string().min(1, "请选择租户"),
    applicationIds: z.array(z.string()).optional(),
    apiIds: z.array(z.string()).optional(),
    quotaType: QuotaType,
    limit: z.number().min(1, "配额限制必须大于0"),
    period: QuotaPeriod.default("MONTHLY"),
    lifecyclePeriod: LifecyclePeriod.default("ONE_YEAR"),
    warningThreshold: z.number().min(0).max(100).default(80), // 警告阈值百分比
    description: z.string().optional(),
  })
  .refine(
    (data) => {
      // 资源类型固定为租户，但需要选择至少一个应用或API
      return (
        (data.applicationIds && data.applicationIds.length > 0) ||
        (data.apiIds && data.apiIds.length > 0)
      );
    },
    {
      message: "请至少选择一个应用或API",
      path: ["applicationIds"],
    },
  );

const UpdateQuotaAllocationSchema = z.object({
  quotaId: z.string(),
  limit: z.number().min(1, "配额限制必须大于0").optional(),
  period: QuotaPeriod.optional(),
  warningThreshold: z.number().min(0).max(100).optional(),
  description: z.string().optional(),
  status: QuotaStatus.optional(),
});

const QuotaListFilterSchema = z.object({
  projectId: z.string(),
  resourceType: ResourceType.optional(),
  resourceId: z.string().optional(),
  quotaType: QuotaType.optional(),
  status: QuotaStatus.optional(),
  search: z.string().optional(),
  searchType: z
    .enum(["all", "tenant", "application", "api", "quota", "status"])
    .default("all"),
  tenantId: z.string().optional(), // 按租户筛选
  applicationId: z.string().optional(), // 按应用筛选
  apiId: z.string().optional(), // 按API筛选
  period: z.enum(["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]).optional(), // 按周期筛选
  usageStatus: z.enum(["normal", "warning", "exceeded"]).optional(), // 按使用状态筛选
  page: z.number().min(0).default(0),
  limit: z.number().min(1).max(100).default(20),
});

// 删除配额分配参数验证
const DeleteQuotaAllocationSchema = z.object({
  projectId: z.string(),
  quotaId: z.string(),
});

// 批量删除配额分配参数验证
const BatchDeleteQuotaAllocationSchema = z.object({
  projectId: z.string(),
  quotaIds: z.array(z.string()).min(1, "请选择要删除的配额分配"),
});

// 批量状态更新参数验证
const BatchUpdateStatusSchema = z.object({
  projectId: z.string(),
  quotaIds: z.array(z.string()).min(1, "请选择要更新的配额分配"),
  status: z.enum([
    "NORMAL",
    "WARNING",
    "EXCEEDED",
    "SUSPENDED",
    "APPROVED",
    "REJECTED",
  ]),
  reason: z.string().optional(),
});

// 计算下次重置时间
function calculateNextResetTime(period: string): Date {
  const now = new Date();
  const resetTime = new Date(now);

  switch (period) {
    case "DAILY":
      resetTime.setDate(now.getDate() + 1);
      resetTime.setHours(0, 0, 0, 0);
      break;
    case "WEEKLY":
      const daysUntilMonday = (7 - now.getDay() + 1) % 7 || 7;
      resetTime.setDate(now.getDate() + daysUntilMonday);
      resetTime.setHours(0, 0, 0, 0);
      break;
    case "MONTHLY":
      resetTime.setMonth(now.getMonth() + 1, 1);
      resetTime.setHours(0, 0, 0, 0);
      break;
    case "YEARLY":
      resetTime.setFullYear(now.getFullYear() + 1, 0, 1);
      resetTime.setHours(0, 0, 0, 0);
      break;
    default:
      resetTime.setMonth(now.getMonth() + 1, 1);
      resetTime.setHours(0, 0, 0, 0);
  }

  return resetTime;
}

// 计算生命周期过期时间
function calculateExpirationTime(lifecyclePeriod: string): Date | null {
  const now = new Date();
  switch (lifecyclePeriod) {
    case "ONE_MONTH":
      return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    case "SIX_MONTHS":
      return new Date(now.getTime() + 6 * 30 * 24 * 60 * 60 * 1000);
    case "ONE_YEAR":
      const oneYearLater = new Date(now);
      oneYearLater.setFullYear(now.getFullYear() + 1);
      return oneYearLater;
    case "NEVER_EXPIRE":
      return null; // 永不过期
    default:
      return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 默认1年
  }
}

// 计算配额状态
function calculateQuotaStatus(
  used: number,
  limit: number,
  warningThreshold: number,
): string {
  const usagePercentage = (used / limit) * 100;

  if (used > limit) {
    return "EXCEEDED";
  } else if (usagePercentage >= warningThreshold) {
    return "WARNING";
  } else {
    return "NORMAL";
  }
}

export const quotaManagementRouter = createTRPCRouter({
  // 获取配额管理统计信息
  stats: protectedProjectProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "quotas:read",
      // });

      // 统计配额分配的状态分布
      const [
        totalCount,
        approvedCount,
        pendingCount,
        suspendedCount,
        rejectedCount,
      ] = await Promise.all([
        // 总配额数
        ctx.prisma.quotaAllocation.count({
          where: { projectId: input.projectId },
        }),
        // 已批准（包括APPROVED和NORMAL状态）
        ctx.prisma.quotaAllocation.count({
          where: {
            projectId: input.projectId,
            status: { in: ["APPROVED", "NORMAL"] },
          },
        }),
        // 待审批
        ctx.prisma.quotaAllocation.count({
          where: {
            projectId: input.projectId,
            status: "PENDING",
          },
        }),
        // 已暂停
        ctx.prisma.quotaAllocation.count({
          where: {
            projectId: input.projectId,
            status: "SUSPENDED",
          },
        }),
        // 已拒绝
        ctx.prisma.quotaAllocation.count({
          where: {
            projectId: input.projectId,
            status: "REJECTED",
          },
        }),
      ]);

      return {
        totalCount,
        approvedCount,
        pendingCount,
        suspendedCount,
        rejectedCount,
        // 计算批准率
        approvalRate:
          totalCount > 0 ? Math.round((approvedCount / totalCount) * 100) : 0,
        // 计算待审批率
        pendingRate:
          totalCount > 0 ? Math.round((pendingCount / totalCount) * 100) : 0,
        // 计算拒绝率
        rejectionRate:
          totalCount > 0 ? Math.round((rejectedCount / totalCount) * 100) : 0,
      };
    }),

  // 获取配额分配列表
  list: protectedProjectProcedure
    .input(QuotaListFilterSchema)
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "quotas:read",
      // });

      const where: any = {
        projectId: input.projectId,
      };

      if (input.resourceType) {
        where.resourceType = input.resourceType;
      }

      if (input.resourceId) {
        where.resourceId = input.resourceId;
      }

      if (input.quotaType) {
        where.quotaType = input.quotaType;
      }

      if (input.status) {
        where.status = input.status;
      }

      // 新增筛选条件
      if (input.tenantId) {
        where.tenantId = input.tenantId;
      }

      if (input.applicationId) {
        where.resourceType = "APPLICATION";
        where.resourceId = input.applicationId;
      }

      if (input.apiId) {
        where.resourceType = "API";
        where.resourceId = input.apiId;
      }

      if (input.period) {
        where.period = input.period;
      }

      // 使用状态筛选（基于使用率计算）
      if (input.usageStatus) {
        // 这里需要在查询后进行过滤，因为使用状态是计算得出的
        // 暂时先保留这个逻辑，在查询结果中进行过滤
      }

      // 增强的搜索逻辑
      if (input.search) {
        const searchTerm = input.search.trim();
        const searchConditions = [];

        switch (input.searchType) {
          case "tenant":
            searchConditions.push({
              Tenant: {
                OR: [
                  { name: { contains: searchTerm, mode: "insensitive" } },
                  {
                    displayName: { contains: searchTerm, mode: "insensitive" },
                  },
                  {
                    description: { contains: searchTerm, mode: "insensitive" },
                  },
                  {
                    contactName: { contains: searchTerm, mode: "insensitive" },
                  },
                  {
                    contactEmail: { contains: searchTerm, mode: "insensitive" },
                  },
                ],
              },
            });
            break;

          case "application":
            searchConditions.push({
              Application: {
                OR: [
                  { name: { contains: searchTerm, mode: "insensitive" } },
                  {
                    description: { contains: searchTerm, mode: "insensitive" },
                  },
                ],
              },
            });
            break;

          case "api":
            searchConditions.push({
              ApiManagement: {
                OR: [
                  { name: { contains: searchTerm, mode: "insensitive" } },
                  {
                    description: { contains: searchTerm, mode: "insensitive" },
                  },
                ],
              },
            });
            break;

          case "quota":
            searchConditions.push(
              { quotaType: { contains: searchTerm, mode: "insensitive" } },
              { description: { contains: searchTerm, mode: "insensitive" } },
            );
            break;

          case "status":
            searchConditions.push({
              status: { contains: searchTerm, mode: "insensitive" },
            });
            break;

          case "all":
          default:
            // 全局搜索 - 搜索所有相关字段
            searchConditions.push(
              // 基础字段
              { description: { contains: searchTerm, mode: "insensitive" } },
              { resourceId: { contains: searchTerm, mode: "insensitive" } },
              { quotaType: { contains: searchTerm, mode: "insensitive" } },
              { status: { contains: searchTerm, mode: "insensitive" } },
              { period: { contains: searchTerm, mode: "insensitive" } },

              // 租户信息
              {
                Tenant: {
                  OR: [
                    { name: { contains: searchTerm, mode: "insensitive" } },
                    {
                      displayName: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                    {
                      description: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                    {
                      contactName: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                    {
                      contactEmail: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  ],
                },
              },

              // 应用信息
              {
                Application: {
                  OR: [
                    { name: { contains: searchTerm, mode: "insensitive" } },
                    {
                      description: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  ],
                },
              },

              // API信息
              {
                ApiManagement: {
                  OR: [
                    { name: { contains: searchTerm, mode: "insensitive" } },
                    {
                      description: {
                        contains: searchTerm,
                        mode: "insensitive",
                      },
                    },
                  ],
                },
              },
            );
            break;
        }

        if (searchConditions.length > 0) {
          where.OR = searchConditions;
        }
      }

      const [quotas, totalCount] = await Promise.all([
        ctx.prisma.quotaAllocation.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: input.page * input.limit,
          take: input.limit,
          include: {
            Tenant: {
              select: {
                id: true,
                name: true,
                displayName: true,
                description: true,
              },
            },
            Application: {
              select: {
                id: true,
                name: true,
                description: true,
                type: true,
              },
            },
            ApiManagement: {
              select: {
                id: true,
                name: true,
                description: true,
                type: true,
              },
            },
            project: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        }),
        ctx.prisma.quotaAllocation.count({ where }),
      ]);

      return {
        quotas,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
        currentPage: input.page,
      };
    }),

  // 创建配额分配
  create: protectedProjectProcedure
    .input(CreateQuotaAllocationSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "quotas:create",
      // });

      // 验证租户是否存在且状态为活跃
      const tenant = await ctx.prisma.tenant.findUnique({
        where: { id: input.tenantId },
      });

      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }

      // 检查租户状态是否为活跃
      if (tenant.status !== "active") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `无法为非活跃租户创建配额分配。租户状态：${tenant.status}，只有活跃状态的租户才能创建配额分配。`,
        });
      }

      // 检查租户是否已激活
      if (!tenant.isActive) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "无法为未激活的租户创建配额分配。请先激活租户。",
        });
      }

      const resetAt = calculateNextResetTime(input.period);
      const expiresAt = calculateExpirationTime(input.lifecyclePeriod);

      const createdQuotas = [];
      const skippedResources = []; // 记录跳过的资源

      // 为选中的应用创建配额
      if (input.applicationIds && input.applicationIds.length > 0) {
        for (const appId of input.applicationIds) {
          // 验证应用是否存在且属于该租户
          const app = await ctx.prisma.application.findFirst({
            where: {
              id: appId,
              projectId: input.projectId, // 确保应用属于当前项目
              // TODO: 添加租户关联验证
            },
          });

          if (!app) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `应用 ${appId} 不存在或不属于当前项目`,
            });
          }

          // 检查应用状态是否为活跃
          if (app.status !== "ACTIVE") {
            skippedResources.push({
              type: "应用",
              name: app.name || app.id,
              reason: `应用状态为 ${app.status}，只有活跃状态的应用才能创建配额分配`,
            });
            continue; // 跳过此应用，继续处理下一个
          }

          // 检查是否已存在相同的配额分配（同一租户不能为同一应用申请重复配额）
          const existingQuota = await ctx.prisma.quotaAllocation.findFirst({
            where: {
              projectId: input.projectId,
              tenantId: input.tenantId,
              resourceType: "APPLICATION",
              resourceId: appId,
              quotaType: input.quotaType,
            },
          });

          if (!existingQuota) {
            const quota = await ctx.prisma.quotaAllocation.create({
              data: {
                projectId: input.projectId,
                resourceType: "APPLICATION",
                resourceId: appId,
                quotaType: input.quotaType,
                limit: input.limit,
                used: 0,
                period: input.period,
                lifecyclePeriod: input.lifecyclePeriod,
                warningThreshold: input.warningThreshold,
                description: input.description,
                status: "PENDING", // 新建配额默认为等待审批状态
                resetAt,
                expiresAt,
                tenantId: input.tenantId,
                applicationId: appId,
              },
            });

            createdQuotas.push(quota);
          } else {
            // 记录跳过的资源
            skippedResources.push({
              type: "应用",
              name: app.name || app.id,
              reason: `已存在 ${input.quotaType} 类型的配额`,
            });
          }
        }
      }

      // 为选中的API创建配额
      if (input.apiIds && input.apiIds.length > 0) {
        for (const apiId of input.apiIds) {
          // 验证API是否存在
          const api = await ctx.prisma.apiManagement.findFirst({
            where: {
              id: apiId,
              projectId: input.projectId,
            },
          });

          if (!api) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `API ${apiId} 不存在或不属于当前项目`,
            });
          }

          // 检查API状态是否为活跃
          if (api.status !== "active") {
            skippedResources.push({
              type: "API",
              name: api.name || api.id,
              reason: `API状态为 ${api.status}，只有活跃状态的API才能创建配额分配`,
            });
            continue; // 跳过此API，继续处理下一个
          }

          // 检查是否已存在相同的配额分配（同一租户不能为同一API申请重复配额）
          const existingQuota = await ctx.prisma.quotaAllocation.findFirst({
            where: {
              projectId: input.projectId,
              tenantId: input.tenantId,
              resourceType: "API",
              resourceId: apiId,
              quotaType: input.quotaType,
            },
          });

          if (!existingQuota) {
            const quota = await ctx.prisma.quotaAllocation.create({
              data: {
                projectId: input.projectId,
                resourceType: "API",
                resourceId: apiId,
                quotaType: input.quotaType,
                limit: input.limit,
                used: 0,
                period: input.period,
                lifecyclePeriod: input.lifecyclePeriod,
                warningThreshold: input.warningThreshold,
                description: input.description,
                status: "PENDING", // 新建配额默认为等待审批状态
                resetAt,
                expiresAt,
                tenantId: input.tenantId,
                apiManagementId: apiId,
              },
            });

            createdQuotas.push(quota);
          } else {
            // 记录跳过的资源
            skippedResources.push({
              type: "API",
              name: api.name || api.id,
              reason: `已存在 ${input.quotaType} 类型的配额`,
            });
          }
        }
      }

      if (createdQuotas.length === 0) {
        // 构建详细的错误消息
        const skippedDetails = skippedResources
          .map(
            (resource) =>
              `${resource.type} "${resource.name}": ${resource.reason}`,
          )
          .join("; ");

        throw new TRPCError({
          code: "CONFLICT",
          message: `无法创建配额分配，所有选中的资源都已存在相同类型的配额。详情: ${skippedDetails}`,
        });
      }

      // 记录审计日志
      for (const quota of createdQuotas) {
        await auditLog({
          session: ctx.session,
          resourceType: "quota_allocation",
          resourceId: quota.id,
          action: "create",
          after: quota,
        });
      }

      // 返回创建结果和跳过信息
      return {
        createdQuotas,
        skippedResources,
        summary: {
          created: createdQuotas.length,
          skipped: skippedResources.length,
          total: createdQuotas.length + skippedResources.length,
        },
      };
    }),

  // 更新配额分配
  update: protectedProjectProcedure
    .input(UpdateQuotaAllocationSchema)
    .mutation(async ({ input, ctx }) => {
      const { quotaId, ...updateData } = input;

      // 获取配额信息以验证权限
      const quota = await ctx.prisma.quotaAllocation.findUnique({
        where: { id: quotaId },
      });

      if (!quota) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "配额分配不存在",
        });
      }

      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: quota.projectId,
      //   scope: "quotas:update",
      // });

      // 如果更新了周期，重新计算重置时间
      let resetAt = quota.resetAt;
      if (updateData.period && updateData.period !== quota.period) {
        resetAt = calculateNextResetTime(updateData.period);
      }

      // 如果更新了限制或使用量，重新计算状态
      let status = updateData.status || quota.status;
      if (updateData.limit) {
        status = calculateQuotaStatus(
          quota.used,
          updateData.limit,
          updateData.warningThreshold || quota.warningThreshold,
        );
      }

      const updatedQuota = await ctx.prisma.quotaAllocation.update({
        where: { id: quotaId },
        data: {
          ...updateData,
          status,
          resetAt,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "quota_allocation",
        resourceId: quotaId,
        action: "update",
        before: quota,
        after: updatedQuota,
      });

      return updatedQuota;
    }),

  // 删除配额分配
  delete: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        quotaId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const quota = await ctx.prisma.quotaAllocation.findUnique({
        where: { id: input.quotaId },
      });

      if (!quota) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "配额分配不存在",
        });
      }

      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: quota.projectId,
      //   scope: "quotas:delete",
      // });

      await ctx.prisma.quotaAllocation.delete({
        where: { id: input.quotaId },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "quota_allocation",
        resourceId: input.quotaId,
        action: "delete",
        before: quota,
      });

      return { success: true };
    }),

  // 批量删除配额分配
  batchDelete: protectedProjectProcedure
    .input(BatchDeleteQuotaAllocationSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "quotas:delete",
      // });

      // 验证所有配额分配是否存在且属于该项目
      const quotas = await ctx.prisma.quotaAllocation.findMany({
        where: {
          id: { in: input.quotaIds },
          projectId: input.projectId,
        },
      });

      if (quotas.length !== input.quotaIds.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "部分配额分配不存在或不属于该项目",
        });
      }

      // 批量删除
      await ctx.prisma.quotaAllocation.deleteMany({
        where: {
          id: { in: input.quotaIds },
          projectId: input.projectId,
        },
      });

      // 记录审计日志
      for (const quota of quotas) {
        await auditLog({
          session: ctx.session,
          resourceType: "quota_allocation",
          resourceId: quota.id,
          action: "batch_delete",
          before: quota,
        });
      }

      return { success: true, deletedCount: quotas.length };
    }),

  // 批量更新状态
  batchUpdateStatus: protectedProjectProcedure
    .input(BatchUpdateStatusSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "quotas:update",
      // });

      // 验证所有配额分配是否存在且属于该项目
      const quotas = await ctx.prisma.quotaAllocation.findMany({
        where: {
          id: { in: input.quotaIds },
          projectId: input.projectId,
        },
      });

      if (quotas.length !== input.quotaIds.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "部分配额分配不存在或不属于该项目",
        });
      }

      // 批量更新状态
      await ctx.prisma.quotaAllocation.updateMany({
        where: {
          id: { in: input.quotaIds },
          projectId: input.projectId,
        },
        data: {
          status: input.status,
          updatedAt: new Date(),
        },
      });

      // 记录审计日志
      for (const quota of quotas) {
        await auditLog({
          session: ctx.session,
          resourceType: "quota_allocation",
          resourceId: quota.id,
          action: "batch_status_update",
          before: quota,
          after: { ...quota, status: input.status },
          details: input.reason ? { reason: input.reason } : undefined,
        });
      }

      return { success: true, updatedCount: quotas.length };
    }),
});

// 验证资源是否存在的辅助函数
async function validateResourceExists(
  prisma: any,
  resourceType: string,
  resourceId: string,
) {
  switch (resourceType) {
    case "TENANT":
      const tenant = await prisma.tenant.findUnique({
        where: { id: resourceId },
        select: { id: true },
      });
      if (!tenant) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "租户不存在",
        });
      }
      break;
    case "APPLICATION":
      const application = await prisma.application.findUnique({
        where: { id: resourceId },
        select: { id: true },
      });
      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }
      break;
    case "API":
      const api = await prisma.apiManagement.findUnique({
        where: { id: resourceId },
        select: { id: true },
      });
      if (!api) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "API不存在",
        });
      }
      break;
    default:
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "不支持的资源类型",
      });
  }
}
