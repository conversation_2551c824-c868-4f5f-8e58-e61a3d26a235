import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod/v4";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { DialogBody, DialogFooter } from "@/src/components/ui/dialog";
import { Loader2 } from "lucide-react";
import {
  useUpdateQuotaAllocation,
  QuotaPeriod,
  QuotaStatus,
  QUOTA_PERIOD_OPTIONS,
  QUOTA_STATUS_OPTIONS,
} from "../hooks/useQuotaManagement";

// 表单验证模式
const editQuotaSchema = z.object({
  limit: z.number().min(1, "配额限制必须大于0"),
  period: z.nativeEnum(QuotaPeriod),
  warningThreshold: z.number().min(0).max(100),
  description: z.string().optional(),
  status: z.nativeEnum(QuotaStatus),
});

type EditQuotaFormData = z.infer<typeof editQuotaSchema>;

interface EditQuotaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  quotaId: string | null;
  initialData?: {
    limit: number;
    period: string;
    warningThreshold: number;
    description?: string;
    status: string;
  };
  onSuccess?: () => void;
}

export const EditQuotaDialog: React.FC<EditQuotaDialogProps> = ({
  open,
  onOpenChange,
  quotaId,
  initialData,
  onSuccess,
}) => {
  const form = useForm<EditQuotaFormData>({
    resolver: zodResolver(editQuotaSchema),
    defaultValues: {
      limit: 0,
      period: QuotaPeriod.MONTHLY,
      warningThreshold: 80,
      description: "",
      status: QuotaStatus.NORMAL,
    },
  });

  const updateMutation = useUpdateQuotaAllocation();

  // 当初始数据变化时更新表单
  useEffect(() => {
    if (initialData) {
      form.reset({
        limit: initialData.limit,
        period: initialData.period as QuotaPeriod,
        warningThreshold: initialData.warningThreshold,
        description: initialData.description || "",
        status: initialData.status as QuotaStatus,
      });
    }
  }, [initialData, form]);

  const onSubmit = async (data: EditQuotaFormData) => {
    if (!quotaId) return;

    try {
      await updateMutation.mutateAsync({
        quotaId,
        ...data,
      });
      
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      // 错误处理由mutation的onError处理
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>编辑配额分配</DialogTitle>
          <DialogDescription>
            修改配额限制、周期和其他设置
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DialogBody>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* 配额限制 */}
                <FormField
                  control={form.control}
                  name="limit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>配额限制 *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="输入配额限制"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        设置资源使用的最大限制
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 配额周期 */}
                <FormField
                  control={form.control}
                  name="period"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>配额周期</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择配额周期" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {QUOTA_PERIOD_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 警告阈值 */}
                <FormField
                  control={form.control}
                  name="warningThreshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>警告阈值 (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          placeholder="80"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        使用率达到此百分比时发出警告
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 配额状态 */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>配额状态</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择配额状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {QUOTA_STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <div 
                                  className={`h-2 w-2 rounded-full bg-${option.color}-500`}
                                />
                                {option.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 描述 */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="输入配额分配的描述信息..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </DialogBody>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={updateMutation.isLoading}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={updateMutation.isLoading}
              >
                {updateMutation.isLoading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                更新配额分配
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
