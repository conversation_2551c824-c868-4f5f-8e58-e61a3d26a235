import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod/v4";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { DialogBody, DialogFooter } from "@/src/components/ui/dialog";
import { Loader2 } from "lucide-react";
import {
  useCreateQuotaAllocation,
  ResourceType,
  QuotaType,
  QuotaPeriod,
  LifecyclePeriod,
  QUOTA_TYPE_OPTIONS,
  QUOTA_PERIOD_OPTIONS,
  LIFECYCLE_PERIOD_OPTIONS,
} from "../hooks/useQuotaManagement";
import { useTenantList } from "@/src/features/tenant-management/hooks/useTenantManagement";
import { useApplications } from "@/src/features/registration/hooks/useApplications";
import { useApiManagementList } from "@/src/features/api-management/hooks/useApiManagement";
import { Checkbox } from "@/src/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { Plus, Trash2 } from "lucide-react";

// 单个配额类型配置
const quotaConfigSchema = z.object({
  quotaType: z.nativeEnum(QuotaType),
  limit: z.number().min(1, "配额限制必须大于0"),
  warningThreshold: z.number().min(0).max(100).default(80),
});

// 表单验证模式
const createQuotaSchema = z
  .object({
    tenantId: z.string().min(1, "请选择租户"),
    applicationIds: z.array(z.string()).optional(),
    apiIds: z.array(z.string()).optional(),
    quotaConfigs: z.array(quotaConfigSchema).min(1, "请至少配置一个配额类型"),
    period: z.nativeEnum(QuotaPeriod).default(QuotaPeriod.MONTHLY),
    lifecyclePeriod: z
      .nativeEnum(LifecyclePeriod)
      .default(LifecyclePeriod.ONE_YEAR),
    description: z.string().optional(),
  })
  .refine(
    (data) => {
      // 需要选择至少一个应用或API
      return (
        (data.applicationIds && data.applicationIds.length > 0) ||
        (data.apiIds && data.apiIds.length > 0)
      );
    },
    {
      message: "请至少选择一个应用或API",
      path: ["applicationIds"],
    },
  );

type CreateQuotaFormData = z.infer<typeof createQuotaSchema>;
type QuotaConfig = z.infer<typeof quotaConfigSchema>;

interface CreateQuotaDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export const CreateQuotaDialog: React.FC<CreateQuotaDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
}) => {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const [selectedTenantId, setSelectedTenantId] = useState<string>("");

  const form = useForm<CreateQuotaFormData>({
    resolver: zodResolver(createQuotaSchema),
    defaultValues: {
      tenantId: "",
      applicationIds: [],
      apiIds: [],
      quotaConfigs: [
        {
          quotaType: QuotaType.API_CALLS,
          limit: 1000,
          warningThreshold: 80,
        },
      ],
      period: QuotaPeriod.MONTHLY,
      lifecyclePeriod: LifecyclePeriod.ONE_YEAR,
    },
  });

  const createMutation = useCreateQuotaAllocation();

  // 获取资源列表 - 只显示活跃状态的资源
  const { data: tenantData } = useTenantList({
    projectId,
    status: "active", // 只获取活跃状态的租户
    limit: 100,
    page: 0,
  });

  const { data: applicationData } = useApplications({
    projectId,
    tenantId: selectedTenantId || undefined, // 基于选中的租户过滤应用
    status: "ACTIVE", // 只获取活跃状态的应用
    limit: 100,
    page: 0,
  });

  const { data: apiData } = useApiManagementList({
    projectId,
    tenantId: selectedTenantId || undefined, // 基于选中的租户过滤API
    status: "active", // 只获取活跃状态的API
    limit: 100,
    page: 0,
  });

  // 监听租户选择变化
  useEffect(() => {
    const tenantId = form.watch("tenantId");
    if (tenantId !== selectedTenantId) {
      setSelectedTenantId(tenantId);
      form.setValue("applicationIds", []); // 清空应用选择
      form.setValue("apiIds", []); // 清空API选择
    }
  }, [form.watch("tenantId")]);

  const onSubmit = async (data: CreateQuotaFormData) => {
    try {
      // 为每个配额类型创建配额分配
      const results = [];
      for (const quotaConfig of data.quotaConfigs) {
        const result = await createMutation.mutateAsync({
          projectId,
          resourceType: ResourceType.TENANT, // 固定为租户类型
          tenantId: data.tenantId,
          applicationIds: data.applicationIds,
          apiIds: data.apiIds,
          quotaType: quotaConfig.quotaType,
          limit: quotaConfig.limit,
          period: data.period,
          lifecyclePeriod: data.lifecyclePeriod,
          warningThreshold: quotaConfig.warningThreshold,
          description: data.description,
        });
        results.push(result);
      }

      form.reset();
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      // 错误处理由mutation的onError处理
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-h-[95vh] max-w-5xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>新建配额分配</DialogTitle>
          <DialogDescription>
            为租户的应用或API创建配额分配，支持精细化资源管理
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DialogBody>
              <div className="grid grid-cols-1 gap-6">
                {/* 租户选择 - 替代资源使用者功能 */}
                <FormField
                  control={form.control}
                  name="tenantId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>选择租户 *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择租户" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tenantData?.tenants?.map((tenant) => (
                            <SelectItem key={tenant.id} value={tenant.id}>
                              <div className="flex flex-col">
                                <span>{tenant.displayName || tenant.name}</span>
                                {tenant.description && (
                                  <span className="text-xs text-muted-foreground">
                                    {tenant.description}
                                  </span>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        选择配额分配的目标租户，然后为租户选择具体的应用或API
                        <br />
                        <span className="text-xs text-muted-foreground">
                          注：仅显示活跃状态的租户
                        </span>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 应用和API选择 - 仅在选择了租户时显示 */}
              {selectedTenantId && (
                <div className="space-y-6">
                  <div className="border-t pt-6">
                    <h3 className="mb-4 text-lg font-medium">
                      选择应用或API（至少选择一个）
                    </h3>

                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      {/* 应用选择 */}
                      <FormField
                        control={form.control}
                        name="applicationIds"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>选择应用</FormLabel>
                            <FormDescription className="text-xs">
                              仅显示活跃状态的应用
                            </FormDescription>
                            <div className="max-h-40 space-y-2 overflow-y-auto rounded-md border p-3">
                              {applicationData?.applications?.map((app) => (
                                <div
                                  key={app.id}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    id={`app-${app.id}`}
                                    checked={
                                      field.value?.includes(app.id) || false
                                    }
                                    onCheckedChange={(checked) => {
                                      const currentIds = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentIds, app.id]);
                                      } else {
                                        field.onChange(
                                          currentIds.filter(
                                            (id) => id !== app.id,
                                          ),
                                        );
                                      }
                                    }}
                                  />
                                  <label
                                    htmlFor={`app-${app.id}`}
                                    className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                  >
                                    {app.name}
                                    {app.description && (
                                      <span className="ml-2 text-xs text-muted-foreground">
                                        {app.description}
                                      </span>
                                    )}
                                  </label>
                                </div>
                              ))}
                              {(!applicationData?.applications ||
                                applicationData.applications.length === 0) && (
                                <p className="text-sm text-muted-foreground">
                                  该租户暂无可用应用
                                </p>
                              )}
                            </div>
                            <FormDescription>
                              选择需要分配配额的应用（可多选）
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* API选择 */}
                      <FormField
                        control={form.control}
                        name="apiIds"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>选择API</FormLabel>
                            <FormDescription className="text-xs">
                              仅显示活跃状态的API
                            </FormDescription>
                            <div className="max-h-40 space-y-2 overflow-y-auto rounded-md border p-3">
                              {apiData?.data?.map((api) => (
                                <div
                                  key={api.id}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    id={`api-${api.id}`}
                                    checked={
                                      field.value?.includes(api.id) || false
                                    }
                                    onCheckedChange={(checked) => {
                                      const currentIds = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentIds, api.id]);
                                      } else {
                                        field.onChange(
                                          currentIds.filter(
                                            (id) => id !== api.id,
                                          ),
                                        );
                                      }
                                    }}
                                  />
                                  <label
                                    htmlFor={`api-${api.id}`}
                                    className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                  >
                                    {api.name}
                                    {api.description && (
                                      <span className="ml-2 text-xs text-muted-foreground">
                                        {api.description}
                                      </span>
                                    )}
                                  </label>
                                </div>
                              ))}
                              {(!apiData?.data ||
                                apiData.data.length === 0) && (
                                <p className="text-sm text-muted-foreground">
                                  该租户暂无可用API
                                </p>
                              )}
                            </div>
                            <FormDescription>
                              选择需要分配配额的API（可多选）
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* 配额类型配置 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium">配额类型配置</h3>
                    <p className="text-sm text-muted-foreground">
                      为选定的资源配置不同类型的配额限制
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const currentConfigs = form.getValues("quotaConfigs");
                      form.setValue("quotaConfigs", [
                        ...currentConfigs,
                        {
                          quotaType: QuotaType.API_CALLS,
                          limit: 1000,
                          warningThreshold: 80,
                        },
                      ]);
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    添加配额类型
                  </Button>
                </div>

                <div className="space-y-4">
                  {form.watch("quotaConfigs").map((config, index) => (
                    <Card key={index} className="p-4">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">
                            配额类型 {index + 1}
                          </CardTitle>
                          {form.watch("quotaConfigs").length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const currentConfigs =
                                  form.getValues("quotaConfigs");
                                form.setValue(
                                  "quotaConfigs",
                                  currentConfigs.filter((_, i) => i !== index),
                                );
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                          {/* 配额类型选择 */}
                          <FormField
                            control={form.control}
                            name={`quotaConfigs.${index}.quotaType`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>配额类型 *</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="选择配额类型" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {QUOTA_TYPE_OPTIONS.map((option) => (
                                      <SelectItem
                                        key={option.value}
                                        value={option.value}
                                      >
                                        <div className="flex flex-col">
                                          <span>{option.label}</span>
                                          <span className="text-xs text-muted-foreground">
                                            {option.description}
                                          </span>
                                        </div>
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* 配额限制 */}
                          <FormField
                            control={form.control}
                            name={`quotaConfigs.${index}.limit`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>配额限制 *</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="输入配额限制"
                                    {...field}
                                    onChange={(e) =>
                                      field.onChange(Number(e.target.value))
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* 警告阈值 */}
                          <FormField
                            control={form.control}
                            name={`quotaConfigs.${index}.warningThreshold`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>警告阈值 (%)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="80"
                                    min="0"
                                    max="100"
                                    {...field}
                                    onChange={(e) =>
                                      field.onChange(Number(e.target.value))
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* 配额周期 */}
                <FormField
                  control={form.control}
                  name="period"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>配额周期</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择配额周期" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {QUOTA_PERIOD_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>配额重置的时间间隔</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 生命周期 */}
                <FormField
                  control={form.control}
                  name="lifecyclePeriod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>生命周期</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择生命周期" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {LIFECYCLE_PERIOD_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex flex-col">
                                <span>{option.label}</span>
                                <span className="text-xs text-muted-foreground">
                                  {option.description}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>配额分配的有效期限</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 描述 */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="输入配额分配的描述信息..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </DialogBody>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={createMutation.isLoading}
              >
                取消
              </Button>
              <Button type="submit" disabled={createMutation.isLoading}>
                {createMutation.isLoading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                创建配额分配
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
