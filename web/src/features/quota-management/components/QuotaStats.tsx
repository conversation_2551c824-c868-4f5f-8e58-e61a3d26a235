import React from "react";
import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON>, Clock, Pause, XCircle } from "lucide-react";
import { StatsCard, StatsGrid } from "@/src/components/ui/stats-card";
import { api } from "@/src/utils/api";

interface QuotaStatsProps {
  projectId: string;
}

export function QuotaStats({ projectId }: QuotaStatsProps) {
  const { data: stats, isLoading } = api.quotaManagement.stats.useQuery(
    { projectId },
    {
      refetchInterval: 30000, // 每30秒刷新一次
    },
  );

  return (
    <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
      <StatsCard
        title="总配额数"
        value={stats?.totalCount ?? 0}
        description="系统中的所有配额分配"
        icon={BarChart3}
        iconClassName="text-blue-500"
        loading={isLoading}
      />
      <StatsCard
        title="已批准"
        value={stats?.approvedCount ?? 0}
        description={`批准率 ${stats?.approvalRate ?? 0}%`}
        icon={CheckCircle}
        iconClassName="text-green-500"
        trend={{
          value: stats?.approvalRate ?? 0,
          label: "批准率",
          isPositive: (stats?.approvalRate ?? 0) >= 70,
        }}
        loading={isLoading}
      />
      <StatsCard
        title="待审批"
        value={stats?.pendingCount ?? 0}
        description={`待审批率 ${stats?.pendingRate ?? 0}%`}
        icon={Clock}
        iconClassName="text-orange-500"
        trend={{
          value: stats?.pendingRate ?? 0,
          label: "待审批率",
          isPositive: (stats?.pendingRate ?? 0) <= 20, // 待审批率低是好事
        }}
        loading={isLoading}
      />
      <StatsCard
        title="已暂停"
        value={stats?.suspendedCount ?? 0}
        description="暂停使用的配额"
        icon={Pause}
        iconClassName="text-yellow-500"
        loading={isLoading}
      />
      <StatsCard
        title="已拒绝"
        value={stats?.rejectedCount ?? 0}
        description={`拒绝率 ${stats?.rejectionRate ?? 0}%`}
        icon={XCircle}
        iconClassName="text-red-500"
        trend={{
          value: stats?.rejectionRate ?? 0,
          label: "拒绝率",
          isPositive: (stats?.rejectionRate ?? 0) <= 10, // 拒绝率低是好事
        }}
        loading={isLoading}
      />
    </div>
  );
}
