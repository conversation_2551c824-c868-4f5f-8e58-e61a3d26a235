import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { useSession } from "next-auth/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/src/components/ui/table";
import { Badge } from "@/src/components/ui/badge";
import { Progress } from "@/src/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/src/components/ui/dropdown-menu";
import { Checkbox } from "@/src/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/src/components/ui/alert-dialog";
import {
  Loader2,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Plus,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Check,
  X,
  Pause,
  Users,
  Settings,
  Database,
  Filter,
  ChevronDown,
  ChevronUp,
  RefreshCw,
} from "lucide-react";
import {
  useQuotaAllocations,
  useDeleteQuotaAllocation,
  useBatchDeleteQuotaAllocation,
  useBatchUpdateStatus,
  QuotaListParams,
  ResourceType,
  QuotaType,
  QuotaStatus,
  SearchType,
  UsageStatus,
  RESOURCE_TYPE_OPTIONS,
  QUOTA_TYPE_OPTIONS,
  QUOTA_STATUS_OPTIONS,
  SEARCH_TYPE_OPTIONS,
  USAGE_STATUS_OPTIONS,
  QUOTA_PERIOD_OPTIONS,
  getQuotaStatusColor,
  getQuotaStatusLabel,
  getQuotaTypeLabel,
  getResourceTypeLabel,
  getQuotaPeriodLabel,
  calculateUsagePercentage,
  formatQuotaValue,
} from "../hooks/useQuotaManagement";

interface QuotaManagementListProps {
  onCreateQuota?: () => void;
  onEditQuota?: (quotaId: string) => void;
}

export const QuotaManagementList: React.FC<QuotaManagementListProps> = ({
  onCreateQuota,
  onEditQuota,
}) => {
  const { t } = useTranslation(["common", "registration"]);
  const router = useRouter();
  const { data: session } = useSession();
  const projectId = router.query.projectId as string;

  // 高级搜索展开状态 (需要在calculateTableHeight之前定义)
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

  // 屏幕尺寸自适应高度计算
  const [screenHeight, setScreenHeight] = useState(
    typeof window !== "undefined" ? window.innerHeight : 1080,
  );

  useEffect(() => {
    const updateScreenHeight = () => {
      setScreenHeight(window.innerHeight);
    };

    if (typeof window !== "undefined") {
      updateScreenHeight();
      window.addEventListener("resize", updateScreenHeight);
      return () => window.removeEventListener("resize", updateScreenHeight);
    }
  }, []);

  // 根据屏幕高度动态计算预留空间（包含分页栏高度）
  const getReservedSpace = useCallback(
    (selectedCount: number) => {
      // 基础页面元素高度
      const pageHeader = 100; // 页面头部 (导航栏 + 面包屑)
      const statsCards = 160; // 统计卡片区域
      const searchArea = 80; // 基础搜索区域
      const advancedFilter = showAdvancedSearch ? 140 : 0; // 高级筛选面板
      const listHeader = 60; // 列表标题区域
      const pagination = 100; // 分页组件高度
      const margins = 60; // 各种边距和间距

      const batchActions = selectedCount > 0 ? 70 : 0; // 批量操作工具栏 (包含边距)
      const baseReserved =
        pageHeader +
        statsCards +
        searchArea +
        listHeader +
        batchActions +
        pagination +
        margins;
      const totalReserved = baseReserved + advancedFilter;

      // 根据屏幕高度调整安全边距
      let safetyBuffer = 50; // 默认安全边距
      if (screenHeight >= 1440) {
        safetyBuffer = 80; // 大屏幕更多安全边距
      } else if (screenHeight >= 1080) {
        safetyBuffer = 60; // 标准屏幕适中安全边距
      } else if (screenHeight >= 768) {
        safetyBuffer = 40; // 中等屏幕减少安全边距
      } else {
        safetyBuffer = 20; // 小屏幕最小安全边距
      }

      return totalReserved + safetyBuffer;
    },
    [screenHeight, showAdvancedSearch],
  );

  // 根据屏幕高度动态计算最小高度
  const getMinHeight = useCallback(() => {
    if (screenHeight >= 1440) {
      return 400; // 大屏幕可以有更大的最小高度
    } else if (screenHeight >= 1080) {
      return 350; // 标准屏幕
    } else if (screenHeight >= 768) {
      return 300; // 中等屏幕
    } else {
      return 250; // 小屏幕
    }
  }, [screenHeight]);

  // 筛选和搜索状态
  const [filters, setFilters] = useState<QuotaListParams>({
    projectId,
    resourceType: undefined,
    quotaType: undefined,
    status: undefined,
    search: "",
    searchType: SearchType.ALL,
    tenantId: undefined,
    applicationId: undefined,
    apiId: undefined,
    period: undefined,
    usageStatus: undefined,
    page: 0,
    limit: 5,
  });

  // 多选状态
  const [selectedQuotas, setSelectedQuotas] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // 获取配额分配列表
  const {
    data: quotaData,
    isLoading,
    error,
    refetch,
  } = useQuotaAllocations(filters);

  // 删除配额分配
  const deleteMutation = useDeleteQuotaAllocation();
  const batchDeleteMutation = useBatchDeleteQuotaAllocation();
  const batchUpdateStatusMutation = useBatchUpdateStatus();

  // 处理筛选变化
  const handleFilterChange = (key: keyof QuotaListParams, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value === "all" ? undefined : value,
      page: 0, // 重置页码
    }));
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters((prev) => ({
      ...prev,
      search: value,
      page: 0,
    }));
  };

  // 处理删除
  const handleDelete = async (quotaId: string) => {
    if (confirm("确定要删除这个配额分配吗？")) {
      await deleteMutation.mutateAsync({
        projectId,
        quotaId,
      });
    }
  };

  // 处理多选
  const handleSelectQuota = (quotaId: string, checked: boolean) => {
    if (checked) {
      setSelectedQuotas((prev) => [...prev, quotaId]);
    } else {
      setSelectedQuotas((prev) => prev.filter((id) => id !== quotaId));
      setSelectAll(false);
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedQuotas(quotaData?.quotas?.map((quota) => quota.id) || []);
    } else {
      setSelectedQuotas([]);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedQuotas.length === 0) return;

    if (confirm(`确定要删除选中的 ${selectedQuotas.length} 个配额分配吗？`)) {
      await batchDeleteMutation.mutateAsync({
        projectId,
        quotaIds: selectedQuotas,
      });
      setSelectedQuotas([]);
      setSelectAll(false);
    }
  };

  // 批量状态更新
  const handleBatchStatusUpdate = async (status: string) => {
    if (selectedQuotas.length === 0) return;

    const statusLabels: Record<string, string> = {
      APPROVED: "批准",
      REJECTED: "拒绝",
      SUSPENDED: "停用",
      NORMAL: "正常",
    };

    if (
      confirm(
        `确定要将选中的 ${selectedQuotas.length} 个配额分配设置为${statusLabels[status]}状态吗？`,
      )
    ) {
      await batchUpdateStatusMutation.mutateAsync({
        projectId,
        quotaIds: selectedQuotas,
        status: status as any,
      });
      setSelectedQuotas([]);
      setSelectAll(false);
    }
  };

  // 单项状态更新（无需确认，直接更新）
  const handleSingleStatusUpdate = async (quotaId: string, status: string) => {
    await batchUpdateStatusMutation.mutateAsync({
      projectId,
      quotaIds: [quotaId],
      status: status as any,
    });
  };

  // 获取状态图标和颜色
  const getStatusIcon = (status: QuotaStatus) => {
    switch (status) {
      case QuotaStatus.PENDING:
        return <Clock className="h-4 w-4 text-blue-500" />;
      case QuotaStatus.APPROVED:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case QuotaStatus.REJECTED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      case QuotaStatus.NORMAL:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case QuotaStatus.WARNING:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case QuotaStatus.EXCEEDED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      case QuotaStatus.SUSPENDED:
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取状态Badge的样式
  const getStatusBadgeClass = (status: QuotaStatus) => {
    switch (status) {
      case QuotaStatus.PENDING:
        return "bg-blue-50 text-blue-700 border-blue-200";
      case QuotaStatus.APPROVED:
        return "bg-green-50 text-green-700 border-green-200";
      case QuotaStatus.REJECTED:
        return "bg-red-50 text-red-700 border-red-200";
      case QuotaStatus.NORMAL:
        return "bg-green-50 text-green-700 border-green-200";
      case QuotaStatus.WARNING:
        return "bg-yellow-50 text-yellow-700 border-yellow-200";
      case QuotaStatus.EXCEEDED:
        return "bg-red-50 text-red-700 border-red-200";
      case QuotaStatus.SUSPENDED:
        return "bg-yellow-50 text-yellow-700 border-yellow-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  // 获取资源名称和图标（应用或API）
  const getResourceInfo = (quota: any) => {
    // 根据资源类型显示对应的应用或API信息
    if (quota.resourceType === "APPLICATION" && quota.Application) {
      return {
        name: quota.Application.name,
        type: "应用",
        icon: <Settings className="h-4 w-4 text-green-500" />,
        description: quota.Application.description,
        resourceType: quota.Application.type,
      };
    } else if (
      (quota.resourceType === "API_MANAGEMENT" ||
        quota.resourceType === "API") &&
      quota.ApiManagement
    ) {
      return {
        name: quota.ApiManagement.name,
        type: "API",
        icon: <Database className="h-4 w-4 text-purple-500" />,
        description: quota.ApiManagement.description,
        resourceType: quota.ApiManagement.type,
      };
    } else if (quota.resourceType === "TENANT" && quota.Tenant) {
      // 如果是租户类型的配额，显示租户信息
      return {
        name: quota.Tenant.displayName || quota.Tenant.name,
        type: "租户",
        icon: <Users className="h-4 w-4 text-blue-500" />,
        description: quota.Tenant.description,
        resourceType: "TENANT",
      };
    }
    return {
      name: "未知资源",
      type: "未知",
      icon: <XCircle className="h-4 w-4 text-gray-500" />,
      description: "",
      resourceType: "UNKNOWN",
    };
  };

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-500">
            加载配额分配列表失败: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 筛选和搜索 */}
      <Card>
        <CardContent className="pt-6">
          {/* 基础搜索行 - 搜索框和操作按钮 */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="搜索配额分配..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* 搜索类型选择 */}
            <Select
              value={filters.searchType || SearchType.ALL}
              onValueChange={(value) => handleFilterChange("searchType", value)}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="搜索范围" />
              </SelectTrigger>
              <SelectContent>
                {SEARCH_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* 高级搜索切换按钮 */}
            <Button
              variant="outline"
              size="default"
              onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
            >
              <Filter className="mr-2 h-4 w-4" />
              高级筛选
              {showAdvancedSearch ? (
                <ChevronUp className="ml-2 h-4 w-4" />
              ) : (
                <ChevronDown className="ml-2 h-4 w-4" />
              )}
            </Button>

            {/* 刷新按钮 */}
            <Button
              variant="outline"
              size="default"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCw
                className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              刷新
            </Button>

            {/* 新建配额分配按钮 */}
            <Button onClick={onCreateQuota}>
              <Plus className="mr-2 h-4 w-4" />
              新建配额分配
            </Button>
          </div>

          {/* 高级搜索面板 */}
          {showAdvancedSearch && (
            <div className="mt-4 space-y-4 rounded-lg border bg-muted/50 p-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">高级筛选选项</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setFilters({
                      projectId,
                      search: "",
                      searchType: SearchType.ALL,
                      page: 0,
                      limit: 20,
                    });
                  }}
                >
                  重置筛选
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                {/* 配额类型 */}
                <div>
                  <label className="text-sm font-medium">配额类型</label>
                  <Select
                    value={filters.quotaType || "all"}
                    onValueChange={(value) =>
                      handleFilterChange("quotaType", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择配额类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有配额类型</SelectItem>
                      {QUOTA_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 状态 */}
                <div>
                  <label className="text-sm font-medium">状态</label>
                  <Select
                    value={filters.status || "all"}
                    onValueChange={(value) =>
                      handleFilterChange("status", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有状态</SelectItem>
                      {QUOTA_STATUS_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 配额周期 */}
                <div>
                  <label className="text-sm font-medium">配额周期</label>
                  <Select
                    value={filters.period || "all"}
                    onValueChange={(value) =>
                      handleFilterChange("period", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择周期" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有周期</SelectItem>
                      {QUOTA_PERIOD_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 使用状态 */}
                <div>
                  <label className="text-sm font-medium">使用状态</label>
                  <Select
                    value={filters.usageStatus || "all"}
                    onValueChange={(value) =>
                      handleFilterChange("usageStatus", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择使用状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有使用状态</SelectItem>
                      {USAGE_STATUS_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <Badge
                            variant="outline"
                            className={`text-${option.color}-600`}
                          >
                            {option.label}
                          </Badge>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 配额分配列表 */}
      <Card>
        <CardHeader>
          <CardTitle>配额分配列表</CardTitle>
          <CardDescription>
            {quotaData?.totalCount || 0} 个配额分配
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* 批量操作工具栏 */}
          {selectedQuotas.length > 0 && (
            <div className="mb-4 flex items-center justify-between rounded-lg border bg-muted/50 p-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">
                  已选择 {selectedQuotas.length} 项
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedQuotas([]);
                    setSelectAll(false);
                  }}
                >
                  取消选择
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusUpdate("APPROVED")}
                  className="text-green-600 hover:bg-green-50 hover:text-green-700"
                >
                  <Check className="mr-2 h-4 w-4" />
                  批准
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusUpdate("REJECTED")}
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <X className="mr-2 h-4 w-4" />
                  拒绝
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchStatusUpdate("SUSPENDED")}
                  className="text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                >
                  <Pause className="mr-2 h-4 w-4" />
                  停用
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认批量删除</AlertDialogTitle>
                      <AlertDialogDescription>
                        确定要删除选中的 {selectedQuotas.length}{" "}
                        个配额分配吗？此操作无法撤销。
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction onClick={handleBatchDelete}>
                        确认删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : quotaData?.quotas?.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              暂无配额分配
            </div>
          ) : (
            <div className="space-y-4">
              {/* 表格容器 - 屏幕自适应高度方案 */}
              <div
                className="overflow-auto rounded-md border bg-background"
                style={{
                  maxHeight: `calc(100vh - ${getReservedSpace(selectedQuotas.length)}px)`,
                  minHeight: `${getMinHeight()}px`,
                }}
              >
                <div className="overflow-x-auto">
                  <Table className="min-w-[1200px]">
                    <TableHeader className="sticky top-0 z-10 border-b bg-background">
                      <TableRow>
                        <TableHead className="w-12 min-w-12">
                          <Checkbox
                            checked={selectAll}
                            onCheckedChange={handleSelectAll}
                            aria-label="全选"
                          />
                        </TableHead>
                        <TableHead className="w-16 min-w-16 text-center">
                          序号
                        </TableHead>
                        <TableHead className="w-52 min-w-52">
                          租户信息
                        </TableHead>
                        <TableHead className="w-52 min-w-52">
                          资源信息
                        </TableHead>
                        <TableHead className="w-28 min-w-28">
                          配额类型
                        </TableHead>
                        <TableHead className="w-24 min-w-24 text-right">
                          限制
                        </TableHead>
                        <TableHead className="w-36 min-w-36">使用率</TableHead>
                        <TableHead className="w-32 min-w-32">状态</TableHead>
                        <TableHead className="w-24 min-w-24">周期</TableHead>
                        <TableHead className="w-36 min-w-36">操作员</TableHead>
                        <TableHead className="w-40 min-w-40">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {quotaData?.quotas?.map((quota, index) => {
                        const usagePercentage = calculateUsagePercentage(
                          quota.used,
                          quota.limit,
                        );
                        const statusColor = getQuotaStatusColor(
                          quota.status as QuotaStatus,
                        );
                        const resourceInfo = getResourceInfo(quota);
                        const isSelected = selectedQuotas.includes(quota.id);
                        const serialNumber =
                          filters.page * filters.limit + index + 1;

                        return (
                          <TableRow
                            key={quota.id}
                            className={isSelected ? "bg-muted/50" : ""}
                          >
                            {/* 多选复选框 */}
                            <TableCell>
                              <Checkbox
                                checked={isSelected}
                                onCheckedChange={(checked) =>
                                  handleSelectQuota(
                                    quota.id,
                                    checked as boolean,
                                  )
                                }
                                aria-label={`选择配额分配 ${quota.id}`}
                              />
                            </TableCell>

                            {/* 序号 */}
                            <TableCell className="text-center text-sm text-muted-foreground">
                              {serialNumber}
                            </TableCell>

                            {/* 租户信息 */}
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-blue-500" />
                                <div>
                                  <div className="font-medium">
                                    {quota.Tenant?.displayName ||
                                      quota.Tenant?.name ||
                                      "未知租户"}
                                  </div>
                                  {quota.Tenant?.description && (
                                    <div className="text-xs text-muted-foreground">
                                      {quota.Tenant.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </TableCell>

                            {/* 资源信息（应用或API） */}
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {resourceInfo.icon}
                                <div>
                                  <div className="font-medium">
                                    {resourceInfo.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {resourceInfo.type}
                                    {resourceInfo.description &&
                                      ` • ${resourceInfo.description}`}
                                  </div>
                                </div>
                              </div>
                            </TableCell>

                            {/* 配额类型 */}
                            <TableCell>
                              <Badge variant="outline">
                                {getQuotaTypeLabel(
                                  quota.quotaType as QuotaType,
                                )}
                              </Badge>
                            </TableCell>

                            {/* 限制 */}
                            <TableCell className="text-right font-mono">
                              {formatQuotaValue(
                                quota.limit,
                                quota.quotaType as QuotaType,
                              )}
                            </TableCell>

                            {/* 使用率 */}
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Progress
                                  value={usagePercentage}
                                  className="w-16"
                                />
                                <span className="text-sm text-muted-foreground">
                                  {usagePercentage}%
                                </span>
                              </div>
                            </TableCell>

                            {/* 状态 */}
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getStatusIcon(quota.status as QuotaStatus)}
                                <Badge
                                  variant="outline"
                                  className={getStatusBadgeClass(
                                    quota.status as QuotaStatus,
                                  )}
                                >
                                  {getQuotaStatusLabel(
                                    quota.status as QuotaStatus,
                                  )}
                                </Badge>
                              </div>
                            </TableCell>

                            {/* 周期 */}
                            <TableCell>
                              {getQuotaPeriodLabel(quota.period as any)}
                            </TableCell>

                            {/* 操作员信息 */}
                            <TableCell>
                              <div className="text-sm">
                                <div className="font-medium">
                                  {session?.user?.name ||
                                    session?.user?.email ||
                                    "未知用户"}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {new Date(quota.createdAt).toLocaleDateString(
                                    "zh-CN",
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            {/* 操作按钮 */}
                            <TableCell>
                              <div className="flex items-center gap-1">
                                {/* 快速状态管理按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleSingleStatusUpdate(
                                      quota.id,
                                      "APPROVED",
                                    )
                                  }
                                  className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700"
                                  title="批准"
                                >
                                  <Check className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleSingleStatusUpdate(
                                      quota.id,
                                      "REJECTED",
                                    )
                                  }
                                  className="h-7 w-7 p-0 text-red-600 hover:bg-red-50 hover:text-red-700"
                                  title="拒绝"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleSingleStatusUpdate(
                                      quota.id,
                                      "SUSPENDED",
                                    )
                                  }
                                  className="h-7 w-7 p-0 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                                  title="停用"
                                >
                                  <Pause className="h-3 w-3" />
                                </Button>

                                {/* 更多操作菜单 */}
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      className="h-7 w-7 p-0"
                                    >
                                      <MoreHorizontal className="h-3 w-3" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() => onEditQuota?.(quota.id)}
                                    >
                                      <Edit className="mr-2 h-4 w-4" />
                                      编辑
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() => handleDelete(quota.id)}
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      删除
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* 分页组件 */}
              {quotaData && quotaData.totalCount > 0 && (
                <div className="flex items-center justify-between rounded-b-md border-t bg-background px-4 py-4">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>
                      显示第 {filters.page * filters.limit + 1} -{" "}
                      {Math.min(
                        (filters.page + 1) * filters.limit,
                        quotaData.totalCount,
                      )}{" "}
                      条， 共 {quotaData.totalCount} 条记录
                    </span>

                    {/* 每页显示数量选择器 */}
                    <div className="flex items-center gap-2">
                      <span>每页显示</span>
                      <Select
                        value={filters.limit.toString()}
                        onValueChange={(value) => {
                          setFilters((prev) => ({
                            ...prev,
                            limit: parseInt(value),
                            page: 0, // 重置到第一页
                          }));
                        }}
                      >
                        <SelectTrigger className="h-8 w-16">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5</SelectItem>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="20">20</SelectItem>
                          <SelectItem value="40">40</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>条</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setFilters((prev) => ({
                          ...prev,
                          page: Math.max(0, prev.page - 1),
                        }))
                      }
                      disabled={filters.page === 0}
                    >
                      上一页
                    </Button>
                    <div className="flex items-center gap-1">
                      {Array.from(
                        {
                          length: Math.ceil(
                            quotaData.totalCount / filters.limit,
                          ),
                        },
                        (_, i) => (
                          <Button
                            key={i}
                            variant={filters.page === i ? "default" : "outline"}
                            size="sm"
                            onClick={() =>
                              setFilters((prev) => ({ ...prev, page: i }))
                            }
                            className="h-8 w-8 p-0"
                          >
                            {i + 1}
                          </Button>
                        ),
                      ).slice(
                        Math.max(0, filters.page - 2),
                        Math.min(
                          Math.ceil(quotaData.totalCount / filters.limit),
                          filters.page + 3,
                        ),
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setFilters((prev) => ({
                          ...prev,
                          page: Math.min(
                            Math.ceil(quotaData.totalCount / filters.limit) - 1,
                            prev.page + 1,
                          ),
                        }))
                      }
                      disabled={
                        filters.page >=
                        Math.ceil(quotaData.totalCount / filters.limit) - 1
                      }
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
