import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// 配额类型枚举
export enum QuotaType {
  API_CALLS = "API_CALLS",
  STORAGE = "STORAGE",
  USERS = "USERS",
  REQUESTS = "REQUESTS",
  BANDWIDTH = "BANDWIDTH",
  COMPUTE_TIME = "COMPUTE_TIME",
}

// 配额周期枚举
export enum QuotaPeriod {
  DAILY = "DAILY",
  WEEKLY = "WEEKLY",
  MONTHLY = "MONTHLY",
  YEARLY = "YEARLY",
}

// 配额状态枚举
export enum QuotaStatus {
  PENDING = "PENDING", // 等待审批
  APPROVED = "APPROVED", // 已批准
  REJECTED = "REJECTED", // 已拒绝
  NORMAL = "NORMAL", // 正常
  WARNING = "WARNING", // 警告
  EXCEEDED = "EXCEEDED", // 超限
  SUSPENDED = "SUSPENDED", // 暂停
}

// 资源类型枚举
export enum ResourceType {
  TENANT = "TENANT",
  APPLICATION = "APPLICATION",
  API = "API",
}

// 生命周期枚举
export enum LifecyclePeriod {
  ONE_MONTH = "ONE_MONTH",
  SIX_MONTHS = "SIX_MONTHS",
  ONE_YEAR = "ONE_YEAR",
  NEVER_EXPIRE = "NEVER_EXPIRE",
}

// 搜索类型枚举
export enum SearchType {
  ALL = "all",
  TENANT = "tenant",
  APPLICATION = "application",
  API = "api",
  QUOTA = "quota",
  STATUS = "status",
}

// 使用状态枚举
export enum UsageStatus {
  NORMAL = "normal",
  WARNING = "warning",
  EXCEEDED = "exceeded",
}

// 配额分配列表查询参数类型
export interface QuotaListParams {
  projectId: string;
  resourceType?: ResourceType;
  resourceId?: string;
  quotaType?: QuotaType;
  status?: QuotaStatus;
  search?: string;
  searchType?: SearchType;
  tenantId?: string;
  applicationId?: string;
  apiId?: string;
  period?: QuotaPeriod;
  usageStatus?: UsageStatus;
  page?: number;
  limit?: number;
}

// 创建配额分配参数类型 - 资源使用者固定为租户，但支持选择应用和API
export interface CreateQuotaAllocationParams {
  projectId: string;
  resourceType: ResourceType;
  tenantId: string;
  applicationIds?: string[];
  apiIds?: string[];
  quotaType: QuotaType;
  limit: number;
  period?: QuotaPeriod;
  lifecyclePeriod?: LifecyclePeriod;
  warningThreshold?: number;
  description?: string;
}

// 更新配额分配参数类型
export interface UpdateQuotaAllocationParams {
  quotaId: string;
  limit?: number;
  period?: QuotaPeriod;
  warningThreshold?: number;
  description?: string;
  status?: QuotaStatus;
}

// 删除配额分配参数类型
export interface DeleteQuotaAllocationParams {
  projectId: string;
  quotaId: string;
}

// 批量删除配额分配参数类型
export interface BatchDeleteQuotaAllocationParams {
  projectId: string;
  quotaIds: string[];
}

// 批量状态更新参数类型
export interface BatchUpdateStatusParams {
  projectId: string;
  quotaIds: string[];
  status:
    | "NORMAL"
    | "WARNING"
    | "EXCEEDED"
    | "SUSPENDED"
    | "APPROVED"
    | "REJECTED";
  reason?: string;
}

// 获取配额统计信息
export function useQuotaStats(projectId: string) {
  return api.quotaManagement.stats.useQuery(
    { projectId },
    {
      refetchInterval: 30000, // 每30秒刷新一次
    },
  );
}

// 获取配额分配列表
export function useQuotaAllocations(params: QuotaListParams) {
  return api.quotaManagement.list.useQuery(params, {
    enabled: !!params.projectId,
    keepPreviousData: true,
  });
}

// 创建配额分配
export function useCreateQuotaAllocation() {
  const queryClient = useQueryClient();

  return api.quotaManagement.create.useMutation({
    onSuccess: () => {
      toast.success("配额分配创建成功");

      // 刷新配额分配列表
      queryClient.invalidateQueries({
        queryKey: [["quotaManagement", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "创建配额分配失败");
    },
  });
}

// 更新配额分配
export function useUpdateQuotaAllocation() {
  const queryClient = useQueryClient();

  return api.quotaManagement.update.useMutation({
    onSuccess: () => {
      toast.success("配额分配更新成功");

      // 刷新配额分配列表
      queryClient.invalidateQueries({
        queryKey: [["quotaManagement", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "更新配额分配失败");
    },
  });
}

// 删除配额分配
export function useDeleteQuotaAllocation() {
  const queryClient = useQueryClient();

  return api.quotaManagement.delete.useMutation({
    onSuccess: () => {
      toast.success("配额分配删除成功");

      // 刷新配额分配列表
      queryClient.invalidateQueries({
        queryKey: [["quotaManagement", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "删除配额分配失败");
    },
  });
}

// 批量删除配额分配
export function useBatchDeleteQuotaAllocation() {
  const queryClient = useQueryClient();

  return api.quotaManagement.batchDelete.useMutation({
    onSuccess: (data) => {
      toast.success(`成功删除 ${data.deletedCount} 个配额分配`);

      // 刷新配额分配列表
      queryClient.invalidateQueries({
        queryKey: [["quotaManagement", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "批量删除配额分配失败");
    },
  });
}

// 批量更新状态
export function useBatchUpdateStatus() {
  const queryClient = useQueryClient();

  return api.quotaManagement.batchUpdateStatus.useMutation({
    onSuccess: (data) => {
      toast.success(`成功更新 ${data.updatedCount} 个配额分配的状态`);

      // 刷新配额分配列表
      queryClient.invalidateQueries({
        queryKey: [["quotaManagement", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "批量更新状态失败");
    },
  });
}

// 配额类型选项
export const QUOTA_TYPE_OPTIONS = [
  {
    value: QuotaType.API_CALLS,
    label: "API调用次数",
    description: "限制API调用的总次数",
  },
  {
    value: QuotaType.STORAGE,
    label: "存储空间",
    description: "限制存储空间使用量(GB)",
  },
  {
    value: QuotaType.USERS,
    label: "用户数量",
    description: "限制用户账户数量",
  },
  {
    value: QuotaType.REQUESTS,
    label: "请求次数",
    description: "限制HTTP请求总次数",
  },
  {
    value: QuotaType.BANDWIDTH,
    label: "带宽使用",
    description: "限制网络带宽使用量(GB)",
  },
  {
    value: QuotaType.COMPUTE_TIME,
    label: "计算时间",
    description: "限制计算资源使用时间(小时)",
  },
];

// 配额周期选项
export const QUOTA_PERIOD_OPTIONS = [
  { value: QuotaPeriod.DAILY, label: "每日" },
  { value: QuotaPeriod.WEEKLY, label: "每周" },
  { value: QuotaPeriod.MONTHLY, label: "每月" },
  { value: QuotaPeriod.YEARLY, label: "每年" },
];

// 生命周期选项
export const LIFECYCLE_PERIOD_OPTIONS = [
  {
    value: LifecyclePeriod.ONE_MONTH,
    label: "1个月",
    description: "配额将在1个月后过期",
  },
  {
    value: LifecyclePeriod.SIX_MONTHS,
    label: "6个月",
    description: "配额将在6个月后过期",
  },
  {
    value: LifecyclePeriod.ONE_YEAR,
    label: "1年",
    description: "配额将在1年后过期",
  },
  {
    value: LifecyclePeriod.NEVER_EXPIRE,
    label: "永不过期",
    description: "配额永远不会过期",
  },
];

// 资源类型选项 - 固定为租户，然后选择具体的应用或API
export const RESOURCE_TYPE_OPTIONS = [
  {
    value: ResourceType.TENANT,
    label: "租户",
    description: "为租户的应用或API分配配额，支持精细化资源管理",
  },
];

// 配额状态选项
export const QUOTA_STATUS_OPTIONS = [
  { value: QuotaStatus.PENDING, label: "等待审批", color: "blue" },
  { value: QuotaStatus.APPROVED, label: "已批准", color: "green" },
  { value: QuotaStatus.REJECTED, label: "已拒绝", color: "red" },
  { value: QuotaStatus.NORMAL, label: "正常", color: "green" },
  { value: QuotaStatus.WARNING, label: "警告", color: "yellow" },
  { value: QuotaStatus.EXCEEDED, label: "超限", color: "red" },
  { value: QuotaStatus.SUSPENDED, label: "暂停", color: "yellow" },
];

// 获取配额状态颜色
export function getQuotaStatusColor(status: QuotaStatus): string {
  const option = QUOTA_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.color || "gray";
}

// 搜索类型选项
export const SEARCH_TYPE_OPTIONS = [
  { value: SearchType.ALL, label: "全部", description: "搜索所有字段" },
  { value: SearchType.TENANT, label: "租户", description: "仅搜索租户信息" },
  {
    value: SearchType.APPLICATION,
    label: "应用",
    description: "仅搜索应用信息",
  },
  { value: SearchType.API, label: "API", description: "仅搜索API信息" },
  {
    value: SearchType.QUOTA,
    label: "配额",
    description: "仅搜索配额类型和描述",
  },
  { value: SearchType.STATUS, label: "状态", description: "仅搜索状态信息" },
];

// 使用状态选项
export const USAGE_STATUS_OPTIONS = [
  {
    value: UsageStatus.NORMAL,
    label: "正常",
    color: "green",
    description: "使用率正常",
  },
  {
    value: UsageStatus.WARNING,
    label: "警告",
    color: "yellow",
    description: "使用率接近限制",
  },
  {
    value: UsageStatus.EXCEEDED,
    label: "超限",
    color: "red",
    description: "使用率超过限制",
  },
];

// 获取配额状态标签
export function getQuotaStatusLabel(status: QuotaStatus): string {
  const option = QUOTA_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.label || status;
}

// 获取配额类型标签
export function getQuotaTypeLabel(type: QuotaType): string {
  const option = QUOTA_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.label || type;
}

// 获取资源类型标签
export function getResourceTypeLabel(type: ResourceType): string {
  const option = RESOURCE_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.label || type;
}

// 获取配额周期标签
export function getQuotaPeriodLabel(period: QuotaPeriod): string {
  const option = QUOTA_PERIOD_OPTIONS.find((opt) => opt.value === period);
  return option?.label || period;
}

// 计算使用率百分比
export function calculateUsagePercentage(used: number, limit: number): number {
  if (limit === 0) return 0;
  return Math.round((used / limit) * 100);
}

// 格式化配额值
export function formatQuotaValue(value: number, type: QuotaType): string {
  switch (type) {
    case QuotaType.STORAGE:
    case QuotaType.BANDWIDTH:
      return `${value} GB`;
    case QuotaType.COMPUTE_TIME:
      return `${value} 小时`;
    case QuotaType.API_CALLS:
    case QuotaType.REQUESTS:
    case QuotaType.USERS:
      return value.toLocaleString();
    default:
      return value.toString();
  }
}

// 获取配额使用状态
export function getQuotaUsageStatus(
  used: number,
  limit: number,
  warningThreshold: number = 80,
) {
  const percentage = calculateUsagePercentage(used, limit);

  if (used > limit) {
    return { status: QuotaStatus.EXCEEDED, percentage };
  } else if (percentage >= warningThreshold) {
    return { status: QuotaStatus.WARNING, percentage };
  } else {
    return { status: QuotaStatus.NORMAL, percentage };
  }
}
