import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// 创建Webhook参数类型
export interface CreateWebhookParams {
  applicationId: string;
  url: string;
  events: string[];
  active?: boolean;
}

// 更新Webhook参数类型
export interface UpdateWebhookParams {
  webhookId: string;
  url?: string;
  events?: string[];
  active?: boolean;
}

// 获取应用的Webhook列表
export function useWebhooks(applicationId: string) {
  return api.webhooks.list.useQuery(
    { applicationId },
    {
      enabled: !!applicationId,
    },
  );
}

// 创建Webhook
export function useCreateWebhook() {
  const queryClient = useQueryClient();

  return api.webhooks.create.useMutation({
    onSuccess: (data) => {
      toast.success("Webhook创建成功");

      // 刷新Webhook列表
      queryClient.invalidateQueries({
        queryKey: [["webhooks", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "创建Webhook失败");
    },
  });
}

// 更新Webhook
export function useUpdateWebhook() {
  const queryClient = useQueryClient();

  return api.webhooks.update.useMutation({
    onSuccess: () => {
      toast.success("Webhook更新成功");

      // 刷新Webhook列表
      queryClient.invalidateQueries({
        queryKey: [["webhooks", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "更新Webhook失败");
    },
  });
}

// 删除Webhook
export function useDeleteWebhook() {
  const queryClient = useQueryClient();

  return api.webhooks.delete.useMutation({
    onSuccess: () => {
      toast.success("Webhook删除成功");

      // 刷新Webhook列表
      queryClient.invalidateQueries({
        queryKey: [["webhooks", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "删除Webhook失败");
    },
  });
}

// 重新生成Webhook密钥
export function useRegenerateWebhookSecret() {
  const queryClient = useQueryClient();

  return api.webhooks.regenerateSecret.useMutation({
    onSuccess: () => {
      toast.success("Webhook密钥重新生成成功");

      // 刷新Webhook列表
      queryClient.invalidateQueries({
        queryKey: [["webhooks", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "重新生成密钥失败");
    },
  });
}

// 测试Webhook
export function useTestWebhook() {
  return api.webhooks.test.useMutation({
    onSuccess: (result) => {
      if (result.success) {
        toast.success(`Webhook测试成功 (${result.status})`);
      } else {
        toast.error(`Webhook测试失败: ${result.error || result.status}`);
      }
    },
    onError: (error) => {
      toast.error(error.message || "测试Webhook失败");
    },
  });
}

// 可用的Webhook事件类型
export const WEBHOOK_EVENTS = [
  {
    value: "application.created",
    label: "应用创建",
    description: "当应用被创建时触发",
  },
  {
    value: "application.updated",
    label: "应用更新",
    description: "当应用信息被更新时触发",
  },
  {
    value: "application.deleted",
    label: "应用删除",
    description: "当应用被删除时触发",
  },
  {
    value: "application.activated",
    label: "应用激活",
    description: "当应用状态变为活跃时触发",
  },
  {
    value: "application.suspended",
    label: "应用暂停",
    description: "当应用被暂停时触发",
  },
  {
    value: "quota.exceeded",
    label: "配额超限",
    description: "当应用配额被超出时触发",
  },
  {
    value: "quota.warning",
    label: "配额警告",
    description: "当应用配额使用达到警告阈值时触发",
  },
  {
    value: "quota.reset",
    label: "配额重置",
    description: "当应用配额被重置时触发",
  },
  {
    value: "api.error",
    label: "API错误",
    description: "当API调用发生错误时触发",
  },
  {
    value: "security.breach",
    label: "安全事件",
    description: "当检测到安全问题时触发",
  },
  {
    value: "test.ping",
    label: "测试事件",
    description: "用于测试Webhook连接的事件",
  },
];

// 获取事件显示信息
export function getWebhookEventInfo(eventType: string) {
  return (
    WEBHOOK_EVENTS.find((event) => event.value === eventType) || {
      value: eventType,
      label: eventType,
      description: "",
    }
  );
}

// 验证Webhook URL
export function validateWebhookUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === "https:" || parsedUrl.protocol === "http:";
  } catch {
    return false;
  }
}

// 格式化Webhook事件列表
export function formatWebhookEvents(events: string[]): string {
  if (events.length === 0) return "无事件";
  if (events.length === 1) return getWebhookEventInfo(events[0]).label;
  if (events.length <= 3) {
    return events.map((event) => getWebhookEventInfo(event).label).join(", ");
  }
  return `${events
    .slice(0, 2)
    .map((event) => getWebhookEventInfo(event).label)
    .join(", ")} 等 ${events.length} 个事件`;
}
