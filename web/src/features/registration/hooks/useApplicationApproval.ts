import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// 应用状态枚举
export enum ApplicationStatus {
  PENDING = "PENDING",
  ACTIVE = "ACTIVE",
  REJECTED = "REJECTED",
  INACTIVE = "INACTIVE",
  SUSPENDED = "SUSPENDED",
}

// 应用状态选项
export const APPLICATION_STATUS_OPTIONS = [
  { value: ApplicationStatus.PENDING, label: "待审批", color: "blue" },
  { value: ApplicationStatus.ACTIVE, label: "已激活", color: "green" },
  { value: ApplicationStatus.REJECTED, label: "已拒绝", color: "red" },
  { value: ApplicationStatus.INACTIVE, label: "未激活", color: "gray" },
  { value: ApplicationStatus.SUSPENDED, label: "已暂停", color: "yellow" },
];

// 获取应用状态颜色
export function getApplicationStatusColor(status: ApplicationStatus): string {
  const option = APPLICATION_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.color || "gray";
}

// 获取应用状态标签
export function getApplicationStatusLabel(status: ApplicationStatus): string {
  const option = APPLICATION_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.label || status;
}

// 使用应用列表
export function useApplicationList(projectId: string, filters?: {
  status?: ApplicationStatus;
  type?: string;
  category?: string;
  search?: string;
}) {
  return api.application.list.useQuery(
    {
      projectId,
      ...filters,
    },
    {
      enabled: !!projectId,
      refetchOnWindowFocus: false,
    }
  );
}

// 使用应用统计
export function useApplicationStats(projectId: string) {
  return api.application.stats.useQuery(
    { projectId },
    {
      enabled: !!projectId,
      refetchOnWindowFocus: false,
    }
  );
}

// 使用单个应用审批
export function useApproveApplication() {
  const queryClient = useQueryClient();

  return api.application.approve.useMutation({
    onSuccess: (data, variables) => {
      toast.success(
        variables.approved ? "应用审批通过" : "应用审批拒绝",
        {
          description: `应用 "${data.name}" 已${variables.approved ? "激活" : "拒绝"}`,
        }
      );

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["application", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["application", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("操作失败", {
        description: error.message,
      });
    },
  });
}

// 使用批量应用审批
export function useBatchApproveApplications() {
  const queryClient = useQueryClient();

  return api.application.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      toast.success(
        variables.approved ? "批量审批通过" : "批量审批拒绝",
        {
          description: `已${variables.approved ? "激活" : "拒绝"} ${data.processedCount} 个应用`,
        }
      );

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["application", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["application", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("批量操作失败", {
        description: error.message,
      });
    },
  });
}

// 使用创建应用
export function useCreateApplication() {
  const queryClient = useQueryClient();

  return api.application.create.useMutation({
    onSuccess: (data) => {
      toast.success("应用创建成功", {
        description: `应用 "${data.name}" 已创建，等待审批`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["application", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["application", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("创建应用失败", {
        description: error.message,
      });
    },
  });
}

// 使用更新应用
export function useUpdateApplication() {
  const queryClient = useQueryClient();

  return api.application.update.useMutation({
    onSuccess: (data) => {
      toast.success("应用更新成功", {
        description: `应用 "${data.name}" 已更新`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["application", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["application", "byId"]],
      });
    },
    onError: (error) => {
      toast.error("更新应用失败", {
        description: error.message,
      });
    },
  });
}

// 使用删除应用
export function useDeleteApplication() {
  const queryClient = useQueryClient();

  return api.application.delete.useMutation({
    onSuccess: () => {
      toast.success("应用删除成功");

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["application", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["application", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("删除应用失败", {
        description: error.message,
      });
    },
  });
}

// 应用类型选项
export const APPLICATION_TYPE_OPTIONS = [
  { value: "WEB", label: "Web应用", icon: "🌐" },
  { value: "MOBILE", label: "移动应用", icon: "📱" },
  { value: "DESKTOP", label: "桌面应用", icon: "💻" },
  { value: "API", label: "API服务", icon: "🔌" },
  { value: "MICROSERVICE", label: "微服务", icon: "⚙️" },
  { value: "OTHER", label: "其他", icon: "📦" },
];

// 获取应用类型标签
export function getApplicationTypeLabel(type: string): string {
  const option = APPLICATION_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.label || type;
}

// 获取应用类型图标
export function getApplicationTypeIcon(type: string): string {
  const option = APPLICATION_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.icon || "📦";
}
