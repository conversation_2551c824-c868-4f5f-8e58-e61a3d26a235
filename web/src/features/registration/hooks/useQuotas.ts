import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// 配额类型枚举
export enum QuotaType {
  API_CALLS = "API_CALLS",
  STORAGE = "STORAGE",
  USERS = "USERS",
  REQUESTS = "REQUESTS",
}

// 配额周期枚举
export enum QuotaPeriod {
  DAILY = "DAILY",
  WEEKLY = "WEEKLY",
  MONTHLY = "MONTHLY",
  YEARLY = "YEARLY",
}

// 创建配额参数类型
export interface CreateQuotaParams {
  applicationId: string;
  quotaType: QuotaType;
  limit: number;
  period?: QuotaPeriod;
}

// 更新配额参数类型
export interface UpdateQuotaParams {
  quotaId: string;
  limit?: number;
  period?: QuotaPeriod;
}

// 获取应用的配额列表
export function useQuotas(applicationId: string) {
  return api.quotas.list.useQuery(
    { applicationId },
    {
      enabled: !!applicationId,
    },
  );
}

// 创建配额
export function useCreateQuota() {
  const queryClient = useQueryClient();

  return api.quotas.create.useMutation({
    onSuccess: () => {
      toast.success("配额创建成功");

      // 刷新配额列表
      queryClient.invalidateQueries({
        queryKey: [["quotas", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "创建配额失败");
    },
  });
}

// 更新配额
export function useUpdateQuota() {
  const queryClient = useQueryClient();

  return api.quotas.update.useMutation({
    onSuccess: () => {
      toast.success("配额更新成功");

      // 刷新配额列表
      queryClient.invalidateQueries({
        queryKey: [["quotas", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "更新配额失败");
    },
  });
}

// 重置配额
export function useResetQuota() {
  const queryClient = useQueryClient();

  return api.quotas.reset.useMutation({
    onSuccess: () => {
      toast.success("配额重置成功");

      // 刷新配额列表
      queryClient.invalidateQueries({
        queryKey: [["quotas", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "重置配额失败");
    },
  });
}

// 删除配额
export function useDeleteQuota() {
  const queryClient = useQueryClient();

  return api.quotas.delete.useMutation({
    onSuccess: () => {
      toast.success("配额删除成功");

      // 刷新配额列表
      queryClient.invalidateQueries({
        queryKey: [["quotas", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "删除配额失败");
    },
  });
}

// 增加配额使用量
export function useIncrementQuotaUsage() {
  const queryClient = useQueryClient();

  return api.quotas.incrementUsage.useMutation({
    onSuccess: (result) => {
      if (result.isExceeded) {
        toast.warning("配额已超出限制");
      }

      // 刷新配额列表
      queryClient.invalidateQueries({
        queryKey: [["quotas", "list"]],
      });
    },
    onError: (error) => {
      toast.error(error.message || "更新配额使用量失败");
    },
  });
}

// 配额类型选项
export const QUOTA_TYPE_OPTIONS = [
  {
    value: QuotaType.API_CALLS,
    label: "API调用",
    description: "API调用次数限制",
    unit: "次",
    icon: "🔗",
  },
  {
    value: QuotaType.STORAGE,
    label: "存储空间",
    description: "数据存储空间限制",
    unit: "MB",
    icon: "💾",
  },
  {
    value: QuotaType.USERS,
    label: "用户数量",
    description: "活跃用户数量限制",
    unit: "个",
    icon: "👥",
  },
  {
    value: QuotaType.REQUESTS,
    label: "请求数量",
    description: "HTTP请求数量限制",
    unit: "次",
    icon: "📡",
  },
];

// 配额周期选项
export const QUOTA_PERIOD_OPTIONS = [
  {
    value: QuotaPeriod.DAILY,
    label: "每日",
    description: "每天重置一次",
  },
  {
    value: QuotaPeriod.WEEKLY,
    label: "每周",
    description: "每周重置一次",
  },
  {
    value: QuotaPeriod.MONTHLY,
    label: "每月",
    description: "每月重置一次",
  },
  {
    value: QuotaPeriod.YEARLY,
    label: "每年",
    description: "每年重置一次",
  },
];

// 获取配额类型显示信息
export function getQuotaTypeInfo(type: QuotaType) {
  return (
    QUOTA_TYPE_OPTIONS.find((option) => option.value === type) || {
      value: type,
      label: type,
      description: "",
      unit: "",
      icon: "📊",
    }
  );
}

// 获取配额周期显示信息
export function getQuotaPeriodInfo(period: QuotaPeriod) {
  return (
    QUOTA_PERIOD_OPTIONS.find((option) => option.value === period) || {
      value: period,
      label: period,
      description: "",
    }
  );
}

// 格式化配额使用量
export function formatQuotaUsage(
  used: number,
  limit: number,
  unit: string,
): string {
  const percentage = limit > 0 ? Math.round((used / limit) * 100) : 0;
  return `${used.toLocaleString()} / ${limit.toLocaleString()} ${unit} (${percentage}%)`;
}

// 获取配额状态颜色
export function getQuotaStatusColor(usagePercentage: number): string {
  if (usagePercentage >= 100) return "red";
  if (usagePercentage >= 80) return "orange";
  if (usagePercentage >= 60) return "yellow";
  return "green";
}

// 获取配额状态文本
export function getQuotaStatusText(
  usagePercentage: number,
  isExceeded: boolean,
): string {
  if (isExceeded) return "已超限";
  if (usagePercentage >= 90) return "即将超限";
  if (usagePercentage >= 80) return "使用较高";
  if (usagePercentage >= 60) return "使用正常";
  return "使用较低";
}

// 计算剩余时间到下次重置
export function getTimeToReset(resetAt: Date | null): string {
  if (!resetAt) return "未设置";

  const now = new Date();
  const reset = new Date(resetAt);
  const diff = reset.getTime() - now.getTime();

  if (diff <= 0) return "已过期";

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) return `${days}天${hours}小时`;
  if (hours > 0) return `${hours}小时${minutes}分钟`;
  return `${minutes}分钟`;
}

// 验证配额限制值
export function validateQuotaLimit(limit: number, type: QuotaType): boolean {
  if (limit <= 0) return false;

  switch (type) {
    case QuotaType.API_CALLS:
    case QuotaType.REQUESTS:
      return limit <= 10000000; // 1000万次
    case QuotaType.STORAGE:
      return limit <= 1000000; // 1TB (以MB为单位)
    case QuotaType.USERS:
      return limit <= 100000; // 10万用户
    default:
      return true;
  }
}
