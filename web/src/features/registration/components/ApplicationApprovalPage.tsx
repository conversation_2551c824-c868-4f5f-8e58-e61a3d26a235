"use client";

import React, { useState } from "react";
import { useRouter } from "next/router";
import { toast } from "sonner";
import { But<PERSON> } from "@/src/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/src/components/ui/table";
import { Checkbox } from "@/src/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/src/components/ui/alert-dialog";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Check,
  X,
  RefreshCw,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Pause,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import { StatsCard } from "@/src/components/ui/stats-card";
import {
  useApplicationList,
  useApplicationStats,
  useApproveApplication,
  useBatchApproveApplications,
  ApplicationStatus,
  APPLICATION_STATUS_OPTIONS,
  APPLICATION_TYPE_OPTIONS,
  getApplicationStatusColor,
  getApplicationStatusLabel,
  getApplicationTypeLabel,
  getApplicationTypeIcon,
} from "../hooks/useApplicationApproval";

interface ApplicationApprovalPageProps {
  projectId: string;
}

export const ApplicationApprovalPage: React.FC<
  ApplicationApprovalPageProps
> = ({ projectId }) => {
  const router = useRouter();

  // 筛选状态
  const [filters, setFilters] = useState({
    status: undefined as ApplicationStatus | undefined,
    type: undefined as string | undefined,
    category: "",
    search: "",
  });

  // 选择状态
  const [selectedApplications, setSelectedApplications] = useState<string[]>(
    [],
  );
  const [selectAll, setSelectAll] = useState(false);

  // 审批对话框状态
  const [approvalDialog, setApprovalDialog] = useState({
    open: false,
    applicationId: null as string | null,
    isBatch: false,
    approved: true,
  });
  const [approvalReason, setApprovalReason] = useState("");

  // 数据查询
  const {
    data: applications,
    isLoading,
    refetch,
  } = useApplicationList(projectId, filters);
  const { data: stats } = useApplicationStats(projectId);

  // 操作Mutations
  const approveApplicationMutation = useApproveApplication();
  const batchApproveMutation = useBatchApproveApplications();

  // 处理筛选
  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedApplications(applications?.data?.map((app) => app.id) || []);
    } else {
      setSelectedApplications([]);
    }
  };

  // 处理单选
  const handleSelectApplication = (applicationId: string, checked: boolean) => {
    if (checked) {
      setSelectedApplications((prev) => [...prev, applicationId]);
    } else {
      setSelectedApplications((prev) =>
        prev.filter((id) => id !== applicationId),
      );
      setSelectAll(false);
    }
  };

  // 单个应用状态更新（参考配额管理逻辑，无需确认直接更新）
  const handleSingleStatusUpdate = async (
    applicationId: string,
    status: string,
  ) => {
    try {
      const approved = status === "ACTIVE";
      await approveApplicationMutation.mutateAsync({
        projectId,
        applicationId,
        approved,
        status: status as any, // 传递具体状态
        reason: undefined, // 快速操作不需要原因
      });
    } catch (error) {
      console.error("更新应用状态失败:", error);
    }
  };

  // 批量状态更新
  const handleBatchStatusUpdate = async (status: string) => {
    if (selectedApplications.length === 0) return;

    const statusLabels: Record<string, string> = {
      ACTIVE: "激活",
      REJECTED: "拒绝",
      SUSPENDED: "暂停",
    };

    if (
      confirm(
        `确定要将选中的 ${selectedApplications.length} 个应用设置为${statusLabels[status]}状态吗？`,
      )
    ) {
      try {
        // 使用批量审批接口，现在支持任意状态切换
        await batchApproveMutation.mutateAsync({
          projectId,
          applicationIds: selectedApplications,
          approved: status === "ACTIVE",
          status: status as any, // 传递具体状态
          reason: undefined,
        });
        setSelectedApplications([]);
        setSelectAll(false);
        toast.success(
          `已成功${statusLabels[status]} ${selectedApplications.length} 个应用`,
        );
      } catch (error) {
        console.error("批量操作失败:", error);
      }
    }
  };

  // 处理审批（保留用于对话框）
  const handleApproval = async () => {
    try {
      if (approvalDialog.isBatch) {
        await batchApproveMutation.mutateAsync({
          projectId,
          applicationIds: selectedApplications,
          approved: approvalDialog.approved,
          reason: approvalReason,
        });
        setSelectedApplications([]);
        setSelectAll(false);
      } else if (approvalDialog.applicationId) {
        await approveApplicationMutation.mutateAsync({
          projectId,
          applicationId: approvalDialog.applicationId,
          approved: approvalDialog.approved,
          reason: approvalReason,
        });
      }

      setApprovalDialog({
        open: false,
        applicationId: null,
        isBatch: false,
        approved: true,
      });
      setApprovalReason("");
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  // 查看详情
  const handleViewDetail = (applicationId: string) => {
    router.push(`/project/${projectId}/applications/${applicationId}`);
  };

  // 编辑应用
  const handleEditApplication = (applicationId: string) => {
    router.push(`/project/${projectId}/applications/${applicationId}/edit`);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">应用审批管理</h1>
          <p className="text-muted-foreground">管理和审批应用注册申请</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isLoading}
        >
          <RefreshCw
            className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
          />
          刷新
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
        <StatsCard
          title="总应用数"
          value={stats?.totalCount ?? 0}
          description="系统中的所有应用"
          icon={Eye}
          iconClassName="text-blue-500"
        />
        <StatsCard
          title="待审批"
          value={stats?.pendingCount ?? 0}
          description="等待审批的应用"
          icon={RefreshCw}
          iconClassName="text-orange-500"
        />
        <StatsCard
          title="已激活"
          value={stats?.activeCount ?? 0}
          description="审批通过的应用"
          icon={Check}
          iconClassName="text-green-500"
        />
        <StatsCard
          title="通过率"
          value={`${stats?.approvalRate ?? 0}%`}
          description="审批通过率"
          icon={Check}
          iconClassName="text-green-500"
        />
      </div>

      {/* 筛选条件 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div className="space-y-2">
              <Label>搜索</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="搜索应用名称、开发商..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>状态</Label>
              <Select
                value={filters.status || ""}
                onValueChange={(value) =>
                  handleFilterChange("status", value || undefined)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部状态</SelectItem>
                  {APPLICATION_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>类型</Label>
              <Select
                value={filters.type || ""}
                onValueChange={(value) =>
                  handleFilterChange("type", value || undefined)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部类型</SelectItem>
                  {APPLICATION_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.icon} {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>分类</Label>
              <Input
                placeholder="输入分类"
                value={filters.category}
                onChange={(e) => handleFilterChange("category", e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作提示 */}
      {selectedApplications.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium">
                  已选择 {selectedApplications.length} 个应用
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedApplications([]);
                    setSelectAll(false);
                  }}
                >
                  取消选择
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setApprovalDialog({
                      open: true,
                      applicationId: null,
                      isBatch: true,
                      approved: true,
                    })
                  }
                  className="text-green-600 hover:bg-green-50 hover:text-green-700"
                >
                  <Check className="mr-2 h-4 w-4" />
                  批量批准
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setApprovalDialog({
                      open: true,
                      applicationId: null,
                      isBatch: true,
                      approved: false,
                    })
                  }
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <X className="mr-2 h-4 w-4" />
                  批量拒绝
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 应用列表 */}
      <Card>
        <CardHeader>
          <CardTitle>应用列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectAll}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>应用信息</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>开发商</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applications?.data?.map((application) => (
                <TableRow key={application.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedApplications.includes(application.id)}
                      onCheckedChange={(checked) =>
                        handleSelectApplication(
                          application.id,
                          checked as boolean,
                        )
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{application.name}</div>
                      {application.description && (
                        <div className="text-sm text-muted-foreground">
                          {application.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{getApplicationTypeIcon(application.type)}</span>
                      <span>{getApplicationTypeLabel(application.type)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        getApplicationStatusColor(
                          application.status as ApplicationStatus,
                        ) === "green"
                          ? "default"
                          : getApplicationStatusColor(
                                application.status as ApplicationStatus,
                              ) === "red"
                            ? "destructive"
                            : "secondary"
                      }
                    >
                      {getApplicationStatusLabel(
                        application.status as ApplicationStatus,
                      )}
                    </Badge>
                  </TableCell>
                  <TableCell>{application.developer}</TableCell>
                  <TableCell>
                    {new Date(application.createdAt).toLocaleDateString(
                      "zh-CN",
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {/* 快速状态管理按钮 - 参考配额管理逻辑，所有状态可互相切换 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleSingleStatusUpdate(application.id, "ACTIVE")
                        }
                        className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700"
                        title="激活"
                      >
                        <Check className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleSingleStatusUpdate(application.id, "REJECTED")
                        }
                        className="h-7 w-7 p-0 text-red-600 hover:bg-red-50 hover:text-red-700"
                        title="拒绝"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleSingleStatusUpdate(application.id, "SUSPENDED")
                        }
                        className="h-7 w-7 p-0 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                        title="暂停"
                      >
                        <Pause className="h-3 w-3" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-7 w-7 p-0">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewDetail(application.id)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              handleEditApplication(application.id)
                            }
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            编辑应用
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 审批对话框 */}
      <AlertDialog
        open={approvalDialog.open}
        onOpenChange={(open) =>
          setApprovalDialog((prev) => ({ ...prev, open }))
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {approvalDialog.approved ? "批准" : "拒绝"}应用
              {approvalDialog.isBatch && `（${selectedApplications.length}个）`}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {approvalDialog.approved
                ? "确定要批准这些应用吗？批准后应用将被激活。"
                : "确定要拒绝这些应用吗？拒绝后应用将无法使用。"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>处理原因</Label>
              <Textarea
                placeholder="请输入处理原因（可选）"
                value={approvalReason}
                onChange={(e) => setApprovalReason(e.target.value)}
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApproval}
              className={
                approvalDialog.approved
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              确认{approvalDialog.approved ? "批准" : "拒绝"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
