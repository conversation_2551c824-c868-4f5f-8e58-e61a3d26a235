import {
  createTRPCRouter,
  protectedProjectProcedure,
} from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { throwIfNoProjectAccess } from "@/src/features/rbac/utils/checkProjectAccess";
import { TRPCError } from "@trpc/server";
import { auditLog } from "@/src/features/audit-logs/auditLog";
import { ApplicationType, ApplicationStatus } from "@langfuse/shared/src/db";
import { randomBytes } from "crypto";

// 输入验证模式
const CreateApplicationSchema = z.object({
  projectId: z.string(),
  name: z.string().min(1, "应用名称不能为空"),
  description: z.string().optional(),
  type: z.nativeEnum(ApplicationType),
  category: z.string().min(1, "应用分类不能为空"),
  version: z.string().default("1.0.0"),
  developer: z.string().min(1, "开发商不能为空"),
  tags: z.array(z.string()).default([]),
  isPublic: z.boolean().default(false),
  autoApprove: z.boolean().default(false),
  serviceConfig: z
    .object({
      endpoint: z.string().url().optional(),
      timeout: z.number().min(1000).max(300000).optional(),
      retryCount: z.number().min(0).max(10).optional(),
      rateLimit: z.number().min(1).optional(),
    })
    .optional(),
  permissions: z.array(z.string()).default([]),
});

const UpdateApplicationSchema = z.object({
  projectId: z.string(), // protectedProjectProcedure 需要这个参数
  applicationId: z.string(),
  name: z.string().min(1, "应用名称不能为空").optional(),
  description: z.string().optional(),
  version: z.string().optional(),
  developer: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  autoApprove: z.boolean().optional(),
  serviceConfig: z
    .object({
      endpoint: z.string().url().optional(),
      timeout: z.number().min(1000).max(300000).optional(),
      retryCount: z.number().min(0).max(10).optional(),
      rateLimit: z.number().min(1).optional(),
    })
    .optional(),
  permissions: z.array(z.string()).optional(),
  status: z.nativeEnum(ApplicationStatus).optional(),
});

const ApplicationFilterSchema = z.object({
  projectId: z.string(),
  status: z.nativeEnum(ApplicationStatus).optional(),
  type: z.nativeEnum(ApplicationType).optional(),
  category: z.string().optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  page: z.number().min(0).default(0),
});

// 生成客户端凭据
function generateClientCredentials() {
  const clientId = `app_${randomBytes(16).toString("hex")}`;
  const clientSecret = `sk_${randomBytes(32).toString("hex")}`;
  return { clientId, clientSecret };
}

export const applicationRouter = createTRPCRouter({
  // 获取应用列表
  list: protectedProjectProcedure
    .input(ApplicationFilterSchema)
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:read",
      // });

      const where: any = {
        projectId: input.projectId,
      };

      if (input.status) {
        where.status = input.status;
      }

      if (input.type) {
        where.type = input.type;
      }

      if (input.category) {
        where.category = input.category;
      }

      if (input.search) {
        where.OR = [
          { name: { contains: input.search, mode: "insensitive" } },
          { description: { contains: input.search, mode: "insensitive" } },
          { developer: { contains: input.search, mode: "insensitive" } },
        ];
      }

      const [applications, totalCount] = await Promise.all([
        ctx.prisma.application.findMany({
          where,
          include: {
            webhooks: true,
            quotas: true,
          },
          orderBy: { createdAt: "desc" },
          skip: input.page * input.limit,
          take: input.limit,
        }),
        ctx.prisma.application.count({ where }),
      ]);

      return {
        applications,
        totalCount,
        totalPages: Math.ceil(totalCount / input.limit),
      };
    }),

  // 获取单个应用详情
  byId: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        applicationId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:read",
      // });

      const application = await ctx.prisma.application.findFirst({
        where: {
          id: input.applicationId,
          projectId: input.projectId,
        },
        include: {
          webhooks: true,
          quotas: true,
        },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      return application;
    }),

  // 创建应用
  create: protectedProjectProcedure
    .input(CreateApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:create",
      // });

      // 检查应用名称是否已存在
      const existingApp = await ctx.prisma.application.findFirst({
        where: {
          projectId: input.projectId,
          name: input.name,
        },
      });

      if (existingApp) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "应用名称已存在",
        });
      }

      // 生成客户端凭据
      const { clientId, clientSecret } = generateClientCredentials();

      // 创建应用（默认为待审批状态）
      const application = await ctx.prisma.application.create({
        data: {
          projectId: input.projectId,
          name: input.name,
          description: input.description,
          type: input.type,
          category: input.category,
          version: input.version,
          developer: input.developer,
          tags: input.tags,
          clientId,
          clientSecret,
          isPublic: input.isPublic,
          autoApprove: input.autoApprove,
          serviceConfig: input.serviceConfig,
          permissions: input.permissions,
          status: "PENDING", // 新建应用默认为待审批状态
          createdBy: ctx.session.user.id,
        },
        include: {
          webhooks: true,
          quotas: true,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "application",
        resourceId: application.id,
        action: "create",
        after: application,
      });

      return application;
    }),

  // 更新应用
  update: protectedProjectProcedure
    .input(UpdateApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      const { projectId, applicationId, ...updateData } = input;

      // 获取应用信息以验证项目权限
      const existingApp = await ctx.prisma.application.findUnique({
        where: { id: applicationId },
        select: { projectId: true },
      });

      if (!existingApp) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: existingApp.projectId,
      //   scope: "applications:update",
      // });

      // 如果更新名称，检查是否重复
      if (updateData.name) {
        const duplicateApp = await ctx.prisma.application.findFirst({
          where: {
            projectId: existingApp.projectId,
            name: updateData.name,
            id: { not: applicationId },
          },
        });

        if (duplicateApp) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "应用名称已存在",
          });
        }
      }

      const application = await ctx.prisma.application.update({
        where: { id: applicationId },
        data: updateData,
        include: {
          webhooks: true,
          quotas: true,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "application",
        resourceId: applicationId,
        action: "update",
        after: application,
      });

      return application;
    }),

  // 删除应用
  delete: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        applicationId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:delete",
      // });

      // 获取应用信息以验证应用属于该项目
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      // 验证应用属于指定项目
      if (application.projectId !== input.projectId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "应用不属于指定项目",
        });
      }

      // 删除应用（级联删除相关数据）
      await ctx.prisma.application.delete({
        where: { id: input.applicationId },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "application",
        resourceId: input.applicationId,
        action: "delete",
        before: application,
      });

      return { success: true };
    }),

  // 审核应用 - 支持灵活状态切换
  approve: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        applicationId: z.string(),
        approved: z.boolean(),
        status: z
          .enum(["ACTIVE", "REJECTED", "SUSPENDED", "INACTIVE"])
          .optional(), // 新增状态参数
        reason: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:update",
      // });

      // 获取应用信息
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      // 验证应用属于指定项目
      if (application.projectId !== input.projectId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "应用不属于指定项目",
        });
      }

      // 支持灵活状态切换 - 移除状态限制，参考配额管理逻辑
      // 任意状态都可以切换到其他状态
      const newStatus =
        input.status || (input.approved ? "ACTIVE" : "REJECTED");

      // 更新应用状态
      const updatedApplication = await ctx.prisma.application.update({
        where: { id: input.applicationId },
        data: {
          status: newStatus,
          updatedAt: new Date(),
        },
        include: {
          webhooks: true,
          quotas: true,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "application",
        resourceId: input.applicationId,
        action: input.approved ? "approve" : "reject",
        before: application,
        after: updatedApplication,
        metadata: input.reason ? { reason: input.reason } : undefined,
      });

      return updatedApplication;
    }),

  // 批量审批应用 - 支持灵活状态切换
  batchApprove: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        applicationIds: z.array(z.string()),
        approved: z.boolean(),
        status: z
          .enum(["ACTIVE", "REJECTED", "SUSPENDED", "INACTIVE"])
          .optional(), // 新增状态参数
        reason: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:update",
      // });

      // 验证所有应用是否存在且属于该项目
      const applications = await ctx.prisma.application.findMany({
        where: {
          id: { in: input.applicationIds },
          projectId: input.projectId,
        },
      });

      if (applications.length !== input.applicationIds.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "部分应用不存在或不属于该项目",
        });
      }

      // 支持灵活状态切换 - 移除状态限制，参考配额管理逻辑
      // 任意状态的应用都可以批量处理

      const newStatus =
        input.status || (input.approved ? "ACTIVE" : "REJECTED");

      // 批量更新应用状态
      await ctx.prisma.application.updateMany({
        where: {
          id: { in: input.applicationIds },
          projectId: input.projectId,
        },
        data: {
          status: newStatus,
          updatedAt: new Date(),
        },
      });

      // 记录审计日志
      for (const application of applications) {
        await auditLog({
          session: ctx.session,
          resourceType: "application",
          resourceId: application.id,
          action: input.approved ? "batch_approve" : "batch_reject",
          before: application,
          metadata: {
            reason: input.reason,
            batchSize: applications.length,
          },
        });
      }

      return {
        success: true,
        processedCount: applications.length,
        status: newStatus,
      };
    }),

  // 获取应用统计信息
  stats: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "applications:read",
      // });

      const [totalCount, activeCount, pendingCount, totalUsage] =
        await Promise.all([
          ctx.prisma.application.count({
            where: { projectId: input.projectId },
          }),
          ctx.prisma.application.count({
            where: {
              projectId: input.projectId,
              status: ApplicationStatus.ACTIVE,
            },
          }),
          ctx.prisma.application.count({
            where: {
              projectId: input.projectId,
              status: ApplicationStatus.PENDING,
            },
          }),
          ctx.prisma.application.aggregate({
            where: { projectId: input.projectId },
            _sum: { usageCount: true },
          }),
        ]);

      return {
        totalCount,
        activeCount,
        pendingCount,
        totalUsage: totalUsage._sum.usageCount || 0,
        activeRate:
          totalCount > 0 ? Math.round((activeCount / totalCount) * 100) : 0,
      };
    }),
});
