import {
  createTRPCRouter,
  protectedProjectProcedure,
} from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { throwIfNoProjectAccess } from "@/src/features/rbac/utils/checkProjectAccess";
import { TRPCError } from "@trpc/server";
import { auditLog } from "@/src/features/audit-logs/auditLog";

// 配额类型枚举
const QuotaType = z.enum(["API_CALLS", "STORAGE", "USERS", "REQUESTS"]);
const QuotaPeriod = z.enum(["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]);
const QuotaStatus = z.enum(["NORMAL", "WARNING", "EXCEEDED", "SUSPENDED"]);

// 输入验证模式
const CreateQuotaSchema = z.object({
  applicationId: z.string(),
  quotaType: QuotaType,
  limit: z.number().min(1, "配额限制必须大于0"),
  period: QuotaPeriod.default("MONTHLY"),
});

const UpdateQuotaSchema = z.object({
  quotaId: z.string(),
  limit: z.number().min(1, "配额限制必须大于0").optional(),
  period: QuotaPeriod.optional(),
});

const ResetQuotaSchema = z.object({
  quotaId: z.string(),
});

// 计算下次重置时间
function calculateNextResetTime(period: string): Date {
  const now = new Date();
  const resetTime = new Date(now);

  switch (period) {
    case "DAILY":
      resetTime.setDate(now.getDate() + 1);
      resetTime.setHours(0, 0, 0, 0);
      break;
    case "WEEKLY":
      const daysUntilMonday = (7 - now.getDay() + 1) % 7 || 7;
      resetTime.setDate(now.getDate() + daysUntilMonday);
      resetTime.setHours(0, 0, 0, 0);
      break;
    case "MONTHLY":
      resetTime.setMonth(now.getMonth() + 1, 1);
      resetTime.setHours(0, 0, 0, 0);
      break;
    case "YEARLY":
      resetTime.setFullYear(now.getFullYear() + 1, 0, 1);
      resetTime.setHours(0, 0, 0, 0);
      break;
    default:
      resetTime.setMonth(now.getMonth() + 1, 1);
      resetTime.setHours(0, 0, 0, 0);
  }

  return resetTime;
}

export const quotaRouter = createTRPCRouter({
  // 获取应用的配额列表
  list: protectedProjectProcedure
    .input(
      z.object({
        applicationId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      // 获取应用信息以验证项目权限
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
        select: { projectId: true },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: application.projectId,
        scope: "applications:read",
      });

      const quotas = await ctx.prisma.applicationQuota.findMany({
        where: { applicationId: input.applicationId },
        orderBy: { createdAt: "desc" },
      });

      // 计算配额使用率
      const quotasWithUsage = quotas.map((quota) => ({
        ...quota,
        usagePercentage:
          quota.limit > 0 ? Math.round((quota.used / quota.limit) * 100) : 0,
        remaining: Math.max(0, quota.limit - quota.used),
        isExceeded: quota.used > quota.limit,
      }));

      return quotasWithUsage;
    }),

  // 创建配额
  create: protectedProjectProcedure
    .input(CreateQuotaSchema)
    .mutation(async ({ input, ctx }) => {
      // 获取应用信息以验证项目权限
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
        select: { projectId: true },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: application.projectId,
        scope: "applications:update",
      });

      // 检查是否已存在相同类型的配额
      const existingQuota = await ctx.prisma.applicationQuota.findFirst({
        where: {
          applicationId: input.applicationId,
          quotaType: input.quotaType,
        },
      });

      if (existingQuota) {
        throw new TRPCError({
          code: "CONFLICT",
          message: `${input.quotaType} 配额已存在`,
        });
      }

      // 计算重置时间
      const resetAt = calculateNextResetTime(input.period);

      const quota = await ctx.prisma.applicationQuota.create({
        data: {
          applicationId: input.applicationId,
          quotaType: input.quotaType,
          limit: input.limit,
          period: input.period,
          resetAt,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "quota",
        resourceId: quota.id,
        action: "create",
        after: quota,
      });

      return quota;
    }),

  // 更新配额
  update: protectedProjectProcedure
    .input(UpdateQuotaSchema)
    .mutation(async ({ input, ctx }) => {
      const { quotaId, ...updateData } = input;

      // 获取配额信息以验证权限
      const quota = await ctx.prisma.applicationQuota.findUnique({
        where: { id: quotaId },
        include: {
          application: {
            select: { projectId: true },
          },
        },
      });

      if (!quota) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "配额不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: quota.application.projectId,
        scope: "applications:update",
      });

      // 如果更新了周期，重新计算重置时间
      let resetAt = quota.resetAt;
      if (updateData.period && updateData.period !== quota.period) {
        resetAt = calculateNextResetTime(updateData.period);
      }

      const updatedQuota = await ctx.prisma.applicationQuota.update({
        where: { id: quotaId },
        data: {
          ...updateData,
          resetAt,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "quota",
        resourceId: quotaId,
        action: "update",
        after: updatedQuota,
      });

      return updatedQuota;
    }),

  // 重置配额使用量
  reset: protectedProjectProcedure
    .input(ResetQuotaSchema)
    .mutation(async ({ input, ctx }) => {
      // 获取配额信息以验证权限
      const quota = await ctx.prisma.applicationQuota.findUnique({
        where: { id: input.quotaId },
        include: {
          application: {
            select: { projectId: true },
          },
        },
      });

      if (!quota) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "配额不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: quota.application.projectId,
        scope: "applications:update",
      });

      // 重置使用量并更新重置时间
      const resetAt = calculateNextResetTime(quota.period);

      const updatedQuota = await ctx.prisma.applicationQuota.update({
        where: { id: input.quotaId },
        data: {
          used: 0,
          resetAt,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "quota",
        resourceId: input.quotaId,
        action: "reset",
        after: updatedQuota,
      });

      return updatedQuota;
    }),

  // 删除配额
  delete: protectedProjectProcedure
    .input(
      z.object({
        quotaId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 获取配额信息以验证权限
      const quota = await ctx.prisma.applicationQuota.findUnique({
        where: { id: input.quotaId },
        include: {
          application: {
            select: { projectId: true },
          },
        },
      });

      if (!quota) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "配额不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: quota.application.projectId,
        scope: "applications:delete",
      });

      await ctx.prisma.applicationQuota.delete({
        where: { id: input.quotaId },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "quota",
        resourceId: input.quotaId,
        action: "delete",
        before: quota,
      });

      return { success: true };
    }),

  // 增加配额使用量（供API调用）
  incrementUsage: protectedProjectProcedure
    .input(
      z.object({
        applicationId: z.string(),
        quotaType: QuotaType,
        amount: z.number().min(1).default(1),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 获取应用信息以验证项目权限
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
        select: { projectId: true },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: application.projectId,
        scope: "applications:read",
      });

      // 查找对应的配额
      const quota = await ctx.prisma.applicationQuota.findFirst({
        where: {
          applicationId: input.applicationId,
          quotaType: input.quotaType,
        },
      });

      if (!quota) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `${input.quotaType} 配额不存在`,
        });
      }

      // 检查是否需要重置配额
      const now = new Date();
      let shouldReset = false;
      let resetAt = quota.resetAt;

      if (quota.resetAt && now >= quota.resetAt) {
        shouldReset = true;
        resetAt = calculateNextResetTime(quota.period);
      }

      // 更新配额使用量
      const updatedQuota = await ctx.prisma.applicationQuota.update({
        where: { id: quota.id },
        data: {
          used: shouldReset ? input.amount : quota.used + input.amount,
          resetAt,
        },
      });

      // 检查是否超出限制
      const isExceeded = updatedQuota.used > updatedQuota.limit;

      return {
        quota: updatedQuota,
        isExceeded,
        remaining: Math.max(0, updatedQuota.limit - updatedQuota.used),
        usagePercentage: Math.round(
          (updatedQuota.used / updatedQuota.limit) * 100,
        ),
      };
    }),
});
