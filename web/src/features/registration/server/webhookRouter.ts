import {
  createTRPCRouter,
  protectedProjectProcedure,
} from "@/src/server/api/trpc";
import { z } from "zod/v4";
import { throwIfNoProjectAccess } from "@/src/features/rbac/utils/checkProjectAccess";
import { TRPCError } from "@trpc/server";
import { auditLog } from "@/src/features/audit-logs/auditLog";
import { randomBytes } from "crypto";

// 输入验证模式
const CreateWebhookSchema = z.object({
  applicationId: z.string(),
  url: z.string().url("请输入有效的URL"),
  events: z.array(z.string()).min(1, "至少选择一个事件"),
  active: z.boolean().default(true),
});

const UpdateWebhookSchema = z.object({
  webhookId: z.string(),
  url: z.string().url("请输入有效的URL").optional(),
  events: z.array(z.string()).optional(),
  active: z.boolean().optional(),
});

export const webhookRouter = createTRPCRouter({
  // 获取应用的Webhook列表
  list: protectedProjectProcedure
    .input(
      z.object({
        applicationId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      // 获取应用信息以验证项目权限
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
        select: { projectId: true },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: application.projectId,
        scope: "applications:read",
      });

      const webhooks = await ctx.prisma.applicationWebhook.findMany({
        where: { applicationId: input.applicationId },
        orderBy: { createdAt: "desc" },
      });

      return webhooks;
    }),

  // 创建Webhook
  create: protectedProjectProcedure
    .input(CreateWebhookSchema)
    .mutation(async ({ input, ctx }) => {
      // 获取应用信息以验证项目权限
      const application = await ctx.prisma.application.findUnique({
        where: { id: input.applicationId },
        select: { projectId: true },
      });

      if (!application) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "应用不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: application.projectId,
        scope: "applications:update",
      });

      // 生成密钥
      const secretKey = `whsec_${randomBytes(32).toString("hex")}`;

      const webhook = await ctx.prisma.applicationWebhook.create({
        data: {
          applicationId: input.applicationId,
          url: input.url,
          events: input.events,
          active: input.active,
          secretKey,
        },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "webhook",
        resourceId: webhook.id,
        action: "create",
        after: webhook,
      });

      return webhook;
    }),

  // 更新Webhook
  update: protectedProjectProcedure
    .input(UpdateWebhookSchema)
    .mutation(async ({ input, ctx }) => {
      const { webhookId, ...updateData } = input;

      // 获取Webhook信息以验证权限
      const webhook = await ctx.prisma.applicationWebhook.findUnique({
        where: { id: webhookId },
        include: {
          application: {
            select: { projectId: true },
          },
        },
      });

      if (!webhook) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Webhook不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: webhook.application.projectId,
        scope: "applications:update",
      });

      const updatedWebhook = await ctx.prisma.applicationWebhook.update({
        where: { id: webhookId },
        data: updateData,
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "webhook",
        resourceId: webhookId,
        action: "update",
        after: updatedWebhook,
      });

      return updatedWebhook;
    }),

  // 删除Webhook
  delete: protectedProjectProcedure
    .input(
      z.object({
        webhookId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 获取Webhook信息以验证权限
      const webhook = await ctx.prisma.applicationWebhook.findUnique({
        where: { id: input.webhookId },
        include: {
          application: {
            select: { projectId: true },
          },
        },
      });

      if (!webhook) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Webhook不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: webhook.application.projectId,
        scope: "applications:delete",
      });

      await ctx.prisma.applicationWebhook.delete({
        where: { id: input.webhookId },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "webhook",
        resourceId: input.webhookId,
        action: "delete",
        before: webhook,
      });

      return { success: true };
    }),

  // 重新生成Webhook密钥
  regenerateSecret: protectedProjectProcedure
    .input(
      z.object({
        webhookId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 获取Webhook信息以验证权限
      const webhook = await ctx.prisma.applicationWebhook.findUnique({
        where: { id: input.webhookId },
        include: {
          application: {
            select: { projectId: true },
          },
        },
      });

      if (!webhook) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Webhook不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: webhook.application.projectId,
        scope: "applications:update",
      });

      // 生成新密钥
      const newSecretKey = `whsec_${randomBytes(32).toString("hex")}`;

      const updatedWebhook = await ctx.prisma.applicationWebhook.update({
        where: { id: input.webhookId },
        data: { secretKey: newSecretKey },
      });

      // 记录审计日志
      await auditLog({
        session: ctx.session,
        resourceType: "webhook",
        resourceId: input.webhookId,
        action: "regenerate_secret",
        after: { secretKey: "***" }, // 不记录实际密钥
      });

      return updatedWebhook;
    }),

  // 测试Webhook
  test: protectedProjectProcedure
    .input(
      z.object({
        webhookId: z.string(),
        testEvent: z.string().default("test.ping"),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 获取Webhook信息以验证权限
      const webhook = await ctx.prisma.applicationWebhook.findUnique({
        where: { id: input.webhookId },
        include: {
          application: {
            select: { projectId: true, name: true },
          },
        },
      });

      if (!webhook) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Webhook不存在",
        });
      }

      throwIfNoProjectAccess({
        session: ctx.session,
        projectId: webhook.application.projectId,
        scope: "applications:read",
      });

      // 构造测试数据
      const testPayload = {
        event: input.testEvent,
        timestamp: new Date().toISOString(),
        application: {
          id: webhook.applicationId,
          name: webhook.application.name,
        },
        data: {
          message: "This is a test webhook event",
        },
      };

      try {
        // 发送测试请求
        const response = await fetch(webhook.url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Webhook-Signature": `sha256=${webhook.secretKey}`, // 简化的签名
          },
          body: JSON.stringify(testPayload),
        });

        const result = {
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          timestamp: new Date().toISOString(),
        };

        // 记录审计日志
        await auditLog({
          session: ctx.session,
          resourceType: "webhook",
          resourceId: input.webhookId,
          action: "test",
          after: result,
        });

        return result;
      } catch (error) {
        const result = {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        };

        // 记录审计日志
        await auditLog({
          session: ctx.session,
          resourceType: "webhook",
          resourceId: input.webhookId,
          action: "test",
          after: result,
        });

        return result;
      }
    }),
});
