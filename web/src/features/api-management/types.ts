// API Management Types
export type ApiType = "openai_compatible" | "ragflow_agent" | "dify" | "other";

export type ApiStatus = "active" | "inactive" | "testing" | "deprecated";

export interface ApiManagement {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  type: ApiType;
  status: ApiStatus;

  // OpenAI Compatible API fields
  baseUrl?: string;
  modelName?: string;
  authKey?: string;

  // Ragflow Agent API fields
  agentId?: string;
  address?: string;
  apiKey?: string;

  // Dify API fields
  difyBaseUrl?: string;
  difyApiKey?: string;
  difyAppId?: string;

  // Other API fields
  customConfig?: Record<string, any>;
  headers?: Record<string, string>;

  // Metadata
  metadata?: Record<string, any>;
  tags: string[];

  createdAt: Date;
  updatedAt: Date;
}

export interface ApiTypeInfo {
  label: string;
  description: string;
  icon: string;
  color: string;
}

export interface ApiStatusInfo {
  label: string;
  description: string;
  color: string;
  variant: "default" | "secondary" | "destructive" | "outline";
}

export const API_TYPES: Record<ApiType, ApiTypeInfo> = {
  openai_compatible: {
    label: "OpenAI兼容",
    description: "兼容OpenAI API格式的接口",
    icon: "🤖",
    color: "blue",
  },
  ragflow_agent: {
    label: "Ragflow智能体",
    description: "Ragflow平台的智能体API",
    icon: "🧠",
    color: "purple",
  },
  dify: {
    label: "Dify接口",
    description: "Dify平台的API接口",
    icon: "⚡",
    color: "green",
  },
  other: {
    label: "其他",
    description: "自定义或其他类型的API",
    icon: "🔧",
    color: "gray",
  },
};

export const API_STATUSES: Record<ApiStatus, ApiStatusInfo> = {
  active: {
    label: "活跃",
    description: "API正常运行中",
    color: "green",
    variant: "default",
  },
  inactive: {
    label: "非活跃",
    description: "API暂时停用",
    color: "gray",
    variant: "secondary",
  },
  testing: {
    label: "测试中",
    description: "API正在测试阶段",
    color: "yellow",
    variant: "outline",
  },
  deprecated: {
    label: "已弃用",
    description: "API已弃用，不建议使用",
    color: "red",
    variant: "destructive",
  },
};

// Helper functions
export const getApiTypeInfo = (type: ApiType): ApiTypeInfo => {
  return API_TYPES[type];
};

export const getApiStatusInfo = (status: ApiStatus): ApiStatusInfo => {
  return API_STATUSES[status];
};

// Form validation helpers
export const getRequiredFieldsForType = (type: ApiType): string[] => {
  switch (type) {
    case "openai_compatible":
      return ["baseUrl", "modelName"];
    case "ragflow_agent":
      return ["agentId", "address", "apiKey"];
    case "dify":
      return ["difyBaseUrl", "difyApiKey", "difyAppId"];
    case "other":
      return [];
    default:
      return [];
  }
};

export const getFieldLabelsForType = (
  type: ApiType,
): Record<string, string> => {
  switch (type) {
    case "openai_compatible":
      return {
        baseUrl: "基础URL",
        modelName: "模型名称",
        authKey: "认证密钥",
      };
    case "ragflow_agent":
      return {
        agentId: "智能体ID",
        address: "服务地址",
        apiKey: "API密钥",
      };
    case "dify":
      return {
        difyBaseUrl: "Dify基础URL",
        difyApiKey: "Dify API密钥",
        difyAppId: "Dify应用ID",
      };
    case "other":
      return {
        customConfig: "自定义配置",
        headers: "请求头",
      };
    default:
      return {};
  }
};

// Legacy constants for backward compatibility
export const API_TYPE_LABELS: Record<ApiType, string> = {
  openai_compatible: API_TYPES.openai_compatible.label,
  ragflow_agent: API_TYPES.ragflow_agent.label,
  dify: API_TYPES.dify.label,
  other: API_TYPES.other.label,
};

export const API_STATUS_LABELS: Record<ApiStatus, string> = {
  active: API_STATUSES.active.label,
  inactive: API_STATUSES.inactive.label,
  testing: API_STATUSES.testing.label,
  deprecated: API_STATUSES.deprecated.label,
};

export const API_STATUS_COLORS: Record<ApiStatus, string> = {
  active: "bg-green-100 text-green-800",
  inactive: "bg-gray-100 text-gray-800",
  testing: "bg-yellow-100 text-yellow-800",
  deprecated: "bg-red-100 text-red-800",
};
