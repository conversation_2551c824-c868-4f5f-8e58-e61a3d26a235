import React, { useState } from "react";
import { useTranslation } from "next-i18next";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Badge } from "@/src/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Trash2,
  Edit,
  Copy,
  TestTube,
  Plus,
  Search,
  Filter,
  Check,
  X,
  Pause,
  MoreHorizontal,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/src/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import {
  useApiManagementList,
  useDeleteApiManagement,
  useTestApiConnection,
} from "../hooks/useApiManagement";
import {
  useUpdateApiStatus,
  useBatchApproveApis,
} from "../hooks/useApiApproval";
import {
  API_TYPE_LABELS,
  API_STATUS_LABELS,
  API_STATUS_COLORS,
} from "../types";
import type { ApiType, ApiStatus } from "../types";
import { CreateApiDialog } from "./CreateApiDialog";
import { EditApiDialog } from "./EditApiDialog";
import { CopyApiDialog } from "./CopyApiDialog";
import { DeleteConfirmDialog } from "@/src/components/DeleteConfirmDialog";

interface ApiManagementListProps {
  projectId: string;
}

export const ApiManagementList: React.FC<ApiManagementListProps> = ({
  projectId,
}) => {
  const { t } = useTranslation(["common", "registration"]);

  // 状态管理
  const [search, setSearch] = useState("");
  const [typeFilter, setTypeFilter] = useState<ApiType | "">("");
  const [statusFilter, setStatusFilter] = useState<ApiStatus | "">("");
  const [page, setPage] = useState(0);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingApi, setEditingApi] = useState<string | null>(null);
  const [copyingApi, setCopyingApi] = useState<string | null>(null);
  const [deletingApi, setDeletingApi] = useState<string | null>(null);

  // API调用
  const {
    data: apiList,
    isLoading,
    error,
  } = useApiManagementList({
    projectId,
    search,
    type: typeFilter && typeFilter !== "all" ? typeFilter : undefined,
    status: statusFilter && statusFilter !== "all" ? statusFilter : undefined,
    page,
    limit: 20,
  });

  const deleteMutation = useDeleteApiManagement();
  const testConnectionMutation = useTestApiConnection();
  const updateApiStatusMutation = useUpdateApiStatus();
  const batchApproveApisMutation = useBatchApproveApis();

  // 单个API状态更新（使用专门的单个操作Hook）
  const handleSingleStatusUpdate = async (apiId: string, status: string) => {
    try {
      console.log(`🔄 更新API状态: ${apiId} -> ${status}`); // 调试日志
      await updateApiStatusMutation.mutateAsync({
        projectId,
        apiIds: [apiId], // 单个API作为数组传递
        approved: status === "ACTIVE",
        status: status.toLowerCase() as any, // 传递具体状态
        reason: undefined, // 快速操作不需要原因
      });
    } catch (error) {
      console.error("更新API状态失败:", error);
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(0);
  };

  // 处理筛选
  const handleTypeFilter = (value: string) => {
    setTypeFilter(value === "all" ? "" : (value as ApiType));
    setPage(0);
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value === "all" ? "" : (value as ApiStatus));
    setPage(0);
  };

  // 处理删除
  const handleDelete = async (apiId: string) => {
    await deleteMutation.mutateAsync({ apiId, projectId });
    setDeletingApi(null);
  };

  // 处理测试连接
  const handleTestConnection = async (apiId: string) => {
    await testConnectionMutation.mutateAsync({ apiId, projectId });
  };

  if (isLoading) {
    return (
      <div className="h-full max-h-screen space-y-6 overflow-y-auto pb-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">{t("common:loading")}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full max-h-screen space-y-6 overflow-y-auto pb-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-red-600">加载失败: {error.message}</div>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="h-full max-h-screen space-y-6 overflow-y-auto pb-8">
        {/* 页面操作 */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-muted-foreground">管理和配置各种API接口</p>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                新增API
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>创建新的API配置</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="搜索API名称或描述..."
                    value={search}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select
                  value={typeFilter || "all"}
                  onValueChange={handleTypeFilter}
                >
                  <SelectTrigger className="w-40">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="API类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    {Object.entries(API_TYPE_LABELS).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={statusFilter || "all"}
                  onValueChange={handleStatusFilter}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {Object.entries(API_STATUS_LABELS).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API列表 */}
        {apiList?.data.length === 0 ? (
          <Card>
            <CardContent className="py-8 text-center">
              <p className="text-muted-foreground">暂无API配置</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                创建第一个API配置
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {apiList?.data.map((api) => (
              <Card
                key={api.id}
                className="transition-shadow hover:shadow-md"
                data-api-id={api.id}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{api.name}</CardTitle>
                      {api.description && (
                        <p className="mt-1 text-sm text-muted-foreground">
                          {api.description}
                        </p>
                      )}
                    </div>
                    <Badge className={API_STATUS_COLORS[api.status]}>
                      {API_STATUS_LABELS[api.status]}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        类型
                      </span>
                      <Badge variant="outline">
                        {API_TYPE_LABELS[api.type]}
                      </Badge>
                    </div>

                    {/* Model Name 显示 */}
                    {api.modelName && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          模型
                        </span>
                        <Badge
                          variant="secondary"
                          className="font-mono text-xs"
                        >
                          {api.modelName}
                        </Badge>
                      </div>
                    )}

                    {api.type === "openai_compatible" && api.baseUrl && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">
                          Base URL:{" "}
                        </span>
                        <span className="font-mono text-xs">{api.baseUrl}</span>
                      </div>
                    )}

                    {api.type === "ragflow_agent" && api.address && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">地址: </span>
                        <span className="font-mono text-xs">{api.address}</span>
                      </div>
                    )}

                    {api.type === "dify" && api.difyBaseUrl && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">
                          Dify URL:{" "}
                        </span>
                        <span className="font-mono text-xs">
                          {api.difyBaseUrl}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-2">
                      <div className="text-xs text-muted-foreground">
                        <div>
                          {new Date(api.createdAt).toLocaleDateString()}
                        </div>
                        <div className="text-xs opacity-50">
                          ID: {api.id.slice(-8)}
                        </div>
                      </div>
                      <div className="flex gap-1">
                        {/* 快速状态管理按钮 - 参考应用注册优化逻辑，所有状态可互相切换 */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleSingleStatusUpdate(api.id, "ACTIVE")
                          }
                          className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700"
                          title="激活"
                        >
                          <Check className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleSingleStatusUpdate(api.id, "INACTIVE")
                          }
                          className="h-7 w-7 p-0 text-red-600 hover:bg-red-50 hover:text-red-700"
                          title="停用"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleSingleStatusUpdate(api.id, "TESTING")
                          }
                          className="h-7 w-7 p-0 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                          title="测试"
                        >
                          <Pause className="h-3 w-3" />
                        </Button>

                        {/* 更多操作菜单 */}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-7 w-7 p-0">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleTestConnection(api.id)}
                            >
                              <TestTube className="mr-2 h-4 w-4" />
                              测试连接
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => setEditingApi(api.id)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              编辑配置
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => setCopyingApi(api.id)}
                            >
                              <Copy className="mr-2 h-4 w-4" />
                              复制配置
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => setDeletingApi(api.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除API
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* 分页 */}
        {apiList && apiList.totalPages > 1 && (
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.max(0, page - 1))}
              disabled={page === 0}
            >
              上一页
            </Button>
            <span className="text-sm text-muted-foreground">
              第 {page + 1} 页，共 {apiList.totalPages} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setPage(Math.min(apiList.totalPages - 1, page + 1))
              }
              disabled={page >= apiList.totalPages - 1}
            >
              下一页
            </Button>
          </div>
        )}

        {/* 对话框 */}
        <CreateApiDialog
          projectId={projectId}
          open={showCreateDialog}
          onOpenChange={setShowCreateDialog}
        />

        {editingApi && (
          <EditApiDialog
            projectId={projectId}
            apiId={editingApi}
            open={!!editingApi}
            onOpenChange={(open) => !open && setEditingApi(null)}
          />
        )}

        {copyingApi && (
          <CopyApiDialog
            projectId={projectId}
            apiId={copyingApi}
            open={!!copyingApi}
            onOpenChange={(open) => !open && setCopyingApi(null)}
          />
        )}

        {deletingApi && (
          <DeleteConfirmDialog
            open={!!deletingApi}
            onOpenChange={(open) => !open && setDeletingApi(null)}
            onConfirm={() => handleDelete(deletingApi)}
            title="删除API配置"
            description="确定要删除这个API配置吗？此操作无法撤销。"
          />
        )}
      </div>
    </TooltipProvider>
  );
};
