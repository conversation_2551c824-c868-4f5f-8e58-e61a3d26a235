import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import { Button } from "@/src/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import {
  useApiManagement,
  useUpdateApiManagement,
  useGetModels,
} from "../hooks/useApiManagement";
import { API_TYPE_LABELS, API_STATUS_LABELS } from "../types";
import type { ApiType, ApiStatus } from "../types";

const EditApiSchema = z.object({
  name: z.string().min(1, "名称不能为空"),
  description: z.string().optional(),
  type: z.enum(["openai_compatible", "ragflow_agent", "dify", "other"]),
  status: z.enum(["active", "inactive", "testing", "deprecated"]),

  // OpenAI Compatible API fields
  baseUrl: z.string().url("请输入有效的URL").optional().or(z.literal("")),
  modelName: z.string().optional(),
  authKey: z.string().optional(),

  // Ragflow Agent API fields
  agentId: z.string().optional(),
  address: z.string().url("请输入有效的URL").optional().or(z.literal("")),
  apiKey: z.string().optional(),

  // Dify API fields
  difyBaseUrl: z.string().url("请输入有效的URL").optional().or(z.literal("")),
  difyApiKey: z.string().optional(),
  difyAppId: z.string().optional(),

  // Other API fields
  customConfigJson: z.string().optional(),
  headersJson: z.string().optional(),

  // Metadata
  tags: z.string().optional(),
});

type EditApiFormData = z.infer<typeof EditApiSchema>;

interface EditApiDialogProps {
  projectId: string;
  apiId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const EditApiDialog: React.FC<EditApiDialogProps> = ({
  projectId,
  apiId,
  open,
  onOpenChange,
}) => {
  const [selectedType, setSelectedType] =
    useState<ApiType>("openai_compatible");
  const [availableModels, setAvailableModels] = useState<
    Array<{ id: string; name: string; description?: string }>
  >([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  const { data: api, isLoading } = useApiManagement({ projectId, apiId });
  const updateMutation = useUpdateApiManagement();
  const getModelsMutation = useGetModels();

  const form = useForm<EditApiFormData>({
    resolver: zodResolver(EditApiSchema),
    defaultValues: {
      name: "",
      description: "",
      type: "openai_compatible",
      status: "active",
      baseUrl: "",
      modelName: "",
      authKey: "",
      agentId: "",
      address: "",
      apiKey: "",
      difyBaseUrl: "",
      difyApiKey: "",
      difyAppId: "",
      customConfigJson: "",
      headersJson: "",
      tags: "",
    },
  });

  // 当API数据加载完成时，填充表单
  useEffect(() => {
    if (api) {
      const formData: EditApiFormData = {
        name: api.name,
        description: api.description || "",
        type: api.type,
        status: api.status,
        baseUrl: api.baseUrl || "",
        modelName: api.modelName || "",
        authKey: api.authKey || "",
        agentId: api.agentId || "",
        address: api.address || "",
        apiKey: api.apiKey || "",
        difyBaseUrl: api.difyBaseUrl || "",
        difyApiKey: api.difyApiKey || "",
        difyAppId: api.difyAppId || "",
        customConfigJson: api.customConfig
          ? JSON.stringify(api.customConfig, null, 2)
          : "",
        headersJson: api.headers ? JSON.stringify(api.headers, null, 2) : "",
        tags: api.tags.join(", "),
      };

      form.reset(formData);
      setSelectedType(api.type);
    }
  }, [api, form]);

  const onSubmit = async (data: EditApiFormData) => {
    try {
      // 解析JSON字段
      let customConfig: Record<string, any> | undefined;
      let headers: Record<string, string> | undefined;

      if (data.customConfigJson) {
        try {
          customConfig = JSON.parse(data.customConfigJson);
        } catch (e) {
          form.setError("customConfigJson", { message: "无效的JSON格式" });
          return;
        }
      }

      if (data.headersJson) {
        try {
          headers = JSON.parse(data.headersJson);
        } catch (e) {
          form.setError("headersJson", { message: "无效的JSON格式" });
          return;
        }
      }

      // 解析标签
      const tags = data.tags
        ? data.tags
            .split(",")
            .map((tag) => tag.trim())
            .filter(Boolean)
        : [];

      await updateMutation.mutateAsync({
        apiId,
        projectId,
        name: data.name,
        description: data.description,
        type: data.type,
        status: data.status,
        baseUrl: data.baseUrl || undefined,
        modelName: data.modelName || undefined,
        authKey: data.authKey || undefined,
        agentId: data.agentId || undefined,
        address: data.address || undefined,
        apiKey: data.apiKey || undefined,
        difyBaseUrl: data.difyBaseUrl || undefined,
        difyApiKey: data.difyApiKey || undefined,
        difyAppId: data.difyAppId || undefined,
        customConfig,
        headers,
        tags,
      });

      onOpenChange(false);
    } catch (error) {
      console.error("更新API配置失败:", error);
    }
  };

  const handleTypeChange = (type: ApiType) => {
    setSelectedType(type);
    form.setValue("type", type);
    // 清空模型列表和选择
    clearModels();
  };

  // 清空模型列表
  const clearModels = () => {
    setAvailableModels([]);
    form.setValue("modelName", "");
  };

  // 获取模型列表
  const handleGetModels = async () => {
    const baseUrl = form.getValues("baseUrl");
    const authKey = form.getValues("authKey");

    if (!baseUrl || !authKey) {
      form.setError("baseUrl", { message: "请先填写API地址" });
      form.setError("authKey", { message: "请先填写认证密钥" });
      return;
    }

    setIsLoadingModels(true);
    try {
      const result = await getModelsMutation.mutateAsync({
        projectId,
        baseUrl,
        authKey,
        type: "openai_compatible",
      });

      if (result.success && result.data) {
        setAvailableModels(result.data);
        // 如果只有一个模型，自动选择
        if (result.data.length === 1) {
          form.setValue("modelName", result.data[0].id);
        }
      }
    } catch (error) {
      console.error("获取模型列表失败:", error);
      setAvailableModels([]);
    } finally {
      setIsLoadingModels(false);
    }
  };

  const renderTypeSpecificFields = () => {
    switch (selectedType) {
      case "openai_compatible":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">OpenAI兼容接口配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="baseUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Base URL *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="http://10.0.154.103:8080/v1"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="authKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>认证密钥 *</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="gpustack_789d4d1cb010c27f_ac63b59a0ef8b495e1f0181356c05464"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 获取模型列表按钮 */}
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleGetModels}
                  disabled={
                    isLoadingModels ||
                    !form.getValues("baseUrl") ||
                    !form.getValues("authKey")
                  }
                  className="flex-shrink-0"
                >
                  {isLoadingModels ? "获取中..." : "获取模型列表"}
                </Button>
                {availableModels.length > 0 && (
                  <span className="self-center text-sm text-muted-foreground">
                    找到 {availableModels.length} 个模型
                  </span>
                )}
              </div>

              <FormField
                control={form.control}
                name="modelName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>模型名称</FormLabel>
                    <FormControl>
                      {availableModels.length > 0 ? (
                        <Select
                          value={field.value || ""}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择模型" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableModels.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                <div className="flex flex-col">
                                  <span>{model.name || model.id}</span>
                                  {model.description && (
                                    <span className="text-xs text-muted-foreground">
                                      {model.description}
                                    </span>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          placeholder="输入模型名称，或点击上方按钮获取"
                          {...field}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        );

      case "ragflow_agent":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Ragflow智能体配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="agentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Agent ID *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="d97bd5f2f42e11ef84b10242ac130006"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>服务地址 *</FormLabel>
                    <FormControl>
                      <Input placeholder="http://***********" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="apiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key *</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="ragflow-EyZjdjOTM0ZjM4YjExZWY5ZDc4MDI0Mm"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        );

      case "dify":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Dify接口配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="difyBaseUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dify Base URL *</FormLabel>
                    <FormControl>
                      <Input placeholder="https://api.dify.ai" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="difyApiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dify API Key *</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="app-xxx" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="difyAppId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>应用ID</FormLabel>
                    <FormControl>
                      <Input placeholder="应用ID（可选）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        );

      case "other":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">其他类型接口配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="customConfigJson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>自定义配置 (JSON)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='{"testUrl": "https://api.example.com/test", "testMethod": "GET"}'
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="headersJson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>自定义请求头 (JSON)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='{"Authorization": "Bearer token", "X-Custom-Header": "value"}'
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">加载中...</div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑API配置</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>名称 *</FormLabel>
                      <FormControl>
                        <Input placeholder="输入API配置名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="输入API配置描述"
                          rows={2}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>API类型 *</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={(value) =>
                            handleTypeChange(value as ApiType)
                          }
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择API类型" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.entries(API_TYPE_LABELS).map(
                              ([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>状态</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择状态" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.entries(API_STATUS_LABELS).map(
                              ([value, label]) => (
                                <SelectItem key={value} value={value}>
                                  {label}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>标签</FormLabel>
                      <FormControl>
                        <Input placeholder="输入标签，用逗号分隔" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* 类型特定配置 */}
            {renderTypeSpecificFields()}

            {/* 操作按钮 */}
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button type="submit" disabled={updateMutation.isLoading}>
                {updateMutation.isLoading ? "保存中..." : "保存"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
