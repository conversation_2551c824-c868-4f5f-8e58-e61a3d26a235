import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/src/components/ui/form";
import { Input } from "@/src/components/ui/input";
import { Textarea } from "@/src/components/ui/textarea";
import { Button } from "@/src/components/ui/button";
import { Loader2, Copy } from "lucide-react";
import {
  useCopyApiManagement,
  useApiManagement,
} from "../hooks/useApiManagement";

// 表单验证schema
const CopyApiSchema = z.object({
  name: z.string().min(1, "名称不能为空").max(100, "名称不能超过100个字符"),
  description: z.string().max(500, "描述不能超过500个字符").optional(),
});

type CopyApiFormData = z.infer<typeof CopyApiSchema>;

interface CopyApiDialogProps {
  projectId: string;
  apiId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CopyApiDialog: React.FC<CopyApiDialogProps> = ({
  projectId,
  apiId,
  open,
  onOpenChange,
}) => {
  const copyMutation = useCopyApiManagement();

  // 获取原始API配置信息
  const { data: originalApi } = useApiManagement({
    projectId,
    apiId: apiId || "",
  });

  const form = useForm<CopyApiFormData>({
    resolver: zodResolver(CopyApiSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // 当对话框打开且有原始API数据时，设置默认值
  React.useEffect(() => {
    if (open && originalApi) {
      form.reset({
        name: `${originalApi.name} - 副本`,
        description: originalApi.description || "",
      });
    }
  }, [open, originalApi, form]);

  const onSubmit = async (data: CopyApiFormData) => {
    if (!apiId) return;

    try {
      await copyMutation.mutateAsync({
        apiId,
        projectId,
        name: data.name,
        description: data.description,
      });

      form.reset();
      onOpenChange(false);
    } catch (error) {
      console.error("复制API配置失败:", error);
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Copy className="h-5 w-5" />
            复制API配置
          </DialogTitle>
          <DialogDescription>
            复制现有的API配置，您可以修改名称和描述，其他配置将保持一致。
            {originalApi && (
              <span className="mt-2 block text-sm">
                正在复制：
                <span className="font-medium">{originalApi.name}</span>
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 复制配置 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">复制配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API名称 *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请输入API名称"
                          {...field}
                          disabled={copyMutation.isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        为复制的API配置设置一个新的名称
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请输入API描述（可选）"
                          rows={3}
                          {...field}
                          disabled={copyMutation.isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        简要描述这个API配置的用途和特点
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={copyMutation.isLoading}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={copyMutation.isLoading || !form.formState.isValid}
              >
                {copyMutation.isLoading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                确认复制
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
