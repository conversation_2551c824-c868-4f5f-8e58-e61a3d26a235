import React from "react";
import { Database, Activity, TestTube, Pause } from "lucide-react";
import { StatsCard, StatsGrid } from "@/src/components/ui/stats-card";
import { api } from "@/src/utils/api";

interface ApiStatsProps {
  projectId: string;
}

export function ApiStats({ projectId }: ApiStatsProps) {
  const { data: stats, isLoading } = api.apiManagement.stats.useQuery(
    { projectId },
    {
      refetchInterval: 30000, // 每30秒刷新一次
    },
  );

  return (
    <StatsGrid className="mb-6">
      <StatsCard
        title="总API数"
        value={stats?.totalCount ?? 0}
        description="系统中的所有API"
        icon={Database}
        iconClassName="text-blue-500"
        loading={isLoading}
      />
      <StatsCard
        title="活跃API"
        value={stats?.activeCount ?? 0}
        description={`活跃率 ${stats?.activeRate ?? 0}%`}
        icon={Activity}
        iconClassName="text-green-500"
        trend={{
          value: stats?.activeRate ?? 0,
          label: "活跃率",
          isPositive: (stats?.activeRate ?? 0) >= 70,
        }}
        loading={isLoading}
      />
      <StatsCard
        title="测试中"
        value={stats?.testingCount ?? 0}
        description="正在测试的API"
        icon={TestTube}
        iconClassName="text-orange-500"
        loading={isLoading}
      />
      <StatsCard
        title="非活跃"
        value={stats?.inactiveCount ?? 0}
        description="非活跃的API"
        icon={Pause}
        iconClassName="text-gray-500"
        loading={isLoading}
      />
    </StatsGrid>
  );
}
