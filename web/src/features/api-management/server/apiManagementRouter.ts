import { z } from "zod";
import { auditLog } from "@/src/features/audit-logs/auditLog";
import { throwIfNoProjectAccess } from "@/src/features/rbac/utils/checkProjectAccess";
import {
  createTRPCRouter,
  protectedProcedure,
  protectedProjectProcedure,
} from "@/src/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Prisma } from "@langfuse/shared/src/db";

// Validation schemas
export const CreateApiManagementSchema = z.object({
  projectId: z.string(),
  name: z.string().min(1, "名称不能为空"),
  description: z.string().optional(),
  type: z.enum(["openai_compatible", "ragflow_agent", "dify", "other"]),
  status: z
    .enum([
      "pending",
      "active",
      "inactive",
      "testing",
      "deprecated",
      "rejected",
    ])
    .default("pending"), // 新建API默认为待审批状态

  // OpenAI Compatible API fields
  baseUrl: z.string().url().optional(),
  modelName: z.string().optional(),
  authKey: z.string().optional(),

  // Ragflow Agent API fields
  agentId: z.string().optional(),
  address: z.string().url().optional(),
  apiKey: z.string().optional(),

  // Dify API fields
  difyBaseUrl: z.string().url().optional(),
  difyApiKey: z.string().optional(),
  difyAppId: z.string().optional(),

  // Other API fields
  customConfig: z.record(z.any()).optional(),
  headers: z.record(z.string()).optional(),

  // Metadata
  metadata: z.record(z.any()).optional(),
  tags: z.array(z.string()).default([]),
});

export const UpdateApiManagementSchema =
  CreateApiManagementSchema.partial().extend({
    apiId: z.string(),
    projectId: z.string(),
  });

export const ListApiManagementSchema = z.object({
  projectId: z.string(),
  type: z
    .enum(["openai_compatible", "ragflow_agent", "dify", "other"])
    .optional(),
  status: z.enum(["active", "inactive", "testing", "deprecated"]).optional(),
  search: z.string().default(""),
  page: z.number().int().min(0).default(0),
  limit: z.number().int().min(1).max(100).default(20),
});

export const GetApiManagementSchema = z.object({
  apiId: z.string(),
  projectId: z.string(),
});

export const DeleteApiManagementSchema = z.object({
  apiId: z.string(),
  projectId: z.string(),
});

export const CopyApiManagementSchema = z.object({
  apiId: z.string(),
  projectId: z.string(),
  name: z.string().min(1, "名称不能为空"),
  description: z.string().optional(),
});

export const TestApiConnectionSchema = z.object({
  apiId: z.string(),
  projectId: z.string(),
});

export type CreateApiManagementInput = z.infer<
  typeof CreateApiManagementSchema
>;
export type UpdateApiManagementInput = z.infer<
  typeof UpdateApiManagementSchema
>;
export type ListApiManagementInput = z.infer<typeof ListApiManagementSchema>;
export type GetApiManagementInput = z.infer<typeof GetApiManagementSchema>;
export type DeleteApiManagementInput = z.infer<
  typeof DeleteApiManagementSchema
>;
export type TestApiConnectionInput = z.infer<typeof TestApiConnectionSchema>;
export type CopyApiManagementInput = z.infer<typeof CopyApiManagementSchema>;

export const apiManagementRouter = createTRPCRouter({
  // 获取API管理统计信息
  stats: protectedProjectProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:read",
      // });

      const [totalCount, activeCount, testingCount, inactiveCount] =
        await Promise.all([
          ctx.prisma.apiManagement.count({
            where: { projectId: input.projectId },
          }),
          ctx.prisma.apiManagement.count({
            where: {
              projectId: input.projectId,
              status: "active",
            },
          }),
          ctx.prisma.apiManagement.count({
            where: {
              projectId: input.projectId,
              status: "testing",
            },
          }),
          ctx.prisma.apiManagement.count({
            where: {
              projectId: input.projectId,
              status: "inactive",
            },
          }),
        ]);

      return {
        totalCount,
        activeCount,
        testingCount,
        inactiveCount,
        activeRate:
          totalCount > 0 ? Math.round((activeCount / totalCount) * 100) : 0,
      };
    }),

  // 创建API配置
  create: protectedProjectProcedure
    .input(CreateApiManagementSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:create",
      // });

      try {
        const api = await ctx.prisma.apiManagement.create({
          data: {
            projectId: input.projectId,
            name: input.name,
            description: input.description,
            type: input.type,
            status: input.status,
            baseUrl: input.baseUrl,
            modelName: input.modelName,
            authKey: input.authKey,
            agentId: input.agentId,
            address: input.address,
            apiKey: input.apiKey,
            difyBaseUrl: input.difyBaseUrl,
            difyApiKey: input.difyApiKey,
            difyAppId: input.difyAppId,
            customConfig: input.customConfig,
            headers: input.headers,
            metadata: input.metadata,
            tags: input.tags,
          },
        });

        await auditLog({
          session: ctx.session,
          resourceType: "apiManagement",
          resourceId: api.id,
          action: "create",
          after: api,
        });

        return api;
      } catch (error) {
        console.error("创建API配置失败:", error);
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") {
            throw new TRPCError({
              code: "CONFLICT",
              message: "API配置名称已存在",
            });
          }
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "创建API配置失败",
        });
      }
    }),

  // 获取API配置列表
  list: protectedProjectProcedure
    .input(ListApiManagementSchema)
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:read",
      // });

      const where: Prisma.ApiManagementWhereInput = {
        projectId: input.projectId,
        ...(input.type && { type: input.type }),
        ...(input.status && { status: input.status }),
        ...(input.search && {
          OR: [
            { name: { contains: input.search, mode: "insensitive" } },
            { description: { contains: input.search, mode: "insensitive" } },
          ],
        }),
      };

      const [apis, totalCount] = await Promise.all([
        ctx.prisma.apiManagement.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: input.page * input.limit,
          take: input.limit,
        }),
        ctx.prisma.apiManagement.count({ where }),
      ]);

      return {
        data: apis,
        totalCount,
        page: input.page,
        limit: input.limit,
        totalPages: Math.ceil(totalCount / input.limit),
      };
    }),

  // 获取单个API配置
  byId: protectedProjectProcedure
    .input(GetApiManagementSchema)
    .query(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:read",
      // });

      const api = await ctx.prisma.apiManagement.findFirst({
        where: {
          id: input.apiId,
          projectId: input.projectId,
        },
      });

      if (!api) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "API配置未找到",
        });
      }

      return api;
    }),

  // 更新API配置
  update: protectedProjectProcedure
    .input(UpdateApiManagementSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:update",
      // });

      const existingApi = await ctx.prisma.apiManagement.findFirst({
        where: {
          id: input.apiId,
          projectId: input.projectId,
        },
      });

      if (!existingApi) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "API配置未找到",
        });
      }

      try {
        const { apiId, projectId, ...updateData } = input;

        const updatedApi = await ctx.prisma.apiManagement.update({
          where: { id: input.apiId },
          data: updateData,
        });

        await auditLog({
          session: ctx.session,
          resourceType: "apiManagement",
          resourceId: updatedApi.id,
          action: "update",
          before: existingApi,
          after: updatedApi,
        });

        return updatedApi;
      } catch (error) {
        console.error("更新API配置失败:", error);
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") {
            throw new TRPCError({
              code: "CONFLICT",
              message: "API配置名称已存在",
            });
          }
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "更新API配置失败",
        });
      }
    }),

  // 批量审批API - 支持灵活状态切换
  batchApprove: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        apiIds: z.array(z.string()),
        approved: z.boolean(),
        status: z
          .enum(["active", "inactive", "testing", "deprecated"])
          .optional(), // 新增状态参数，只使用数据库中存在的状态
        reason: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:update",
      // });

      // 验证所有API是否存在且属于该项目
      const apis = await ctx.prisma.apiManagement.findMany({
        where: {
          id: { in: input.apiIds },
          projectId: input.projectId,
        },
      });

      if (apis.length !== input.apiIds.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "部分API不存在或不属于该项目",
        });
      }

      // 支持灵活状态切换 - 移除状态限制，参考应用注册优化逻辑
      // 任意状态的API都可以批量处理

      const newStatus =
        input.status || (input.approved ? "active" : "inactive");

      // 批量更新API状态
      await ctx.prisma.apiManagement.updateMany({
        where: {
          id: { in: input.apiIds },
          projectId: input.projectId,
        },
        data: {
          status: newStatus,
          updatedAt: new Date(),
        },
      });

      // 记录审计日志
      for (const api of apis) {
        await auditLog({
          session: ctx.session,
          resourceType: "apiManagement",
          resourceId: api.id,
          action: input.approved ? "batch_approve" : "batch_reject",
          before: api,
          metadata: {
            reason: input.reason,
            batchSize: apis.length,
          },
        });
      }

      return {
        success: true,
        processedCount: apis.length,
        status: newStatus,
      };
    }),

  // 删除API配置
  delete: protectedProjectProcedure
    .input(DeleteApiManagementSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:delete",
      // });

      const existingApi = await ctx.prisma.apiManagement.findFirst({
        where: {
          id: input.apiId,
          projectId: input.projectId,
        },
      });

      if (!existingApi) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "API配置未找到",
        });
      }

      try {
        await ctx.prisma.apiManagement.delete({
          where: { id: input.apiId },
        });

        await auditLog({
          session: ctx.session,
          resourceType: "apiManagement",
          resourceId: input.apiId,
          action: "delete",
          before: existingApi,
        });

        return { success: true };
      } catch (error) {
        console.error("删除API配置失败:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "删除API配置失败",
        });
      }
    }),

  // 复制API配置
  copy: protectedProjectProcedure
    .input(CopyApiManagementSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:create",
      // });

      try {
        // 获取原始API配置
        const originalApi = await ctx.prisma.apiManagement.findUnique({
          where: { id: input.apiId },
        });

        if (!originalApi) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "API配置不存在",
          });
        }

        // 检查项目权限
        if (originalApi.projectId !== input.projectId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "无权限访问此API配置",
          });
        }

        // 创建复制的API配置
        const copiedApi = await ctx.prisma.apiManagement.create({
          data: {
            projectId: input.projectId,
            name: input.name,
            description: input.description,
            type: originalApi.type,
            status: "inactive", // 复制的API默认为非活跃状态
            baseUrl: originalApi.baseUrl,
            modelName: originalApi.modelName,
            authKey: originalApi.authKey,
            agentId: originalApi.agentId,
            address: originalApi.address,
            apiKey: originalApi.apiKey,
            difyBaseUrl: originalApi.difyBaseUrl,
            difyApiKey: originalApi.difyApiKey,
            difyAppId: originalApi.difyAppId,
            customConfig: originalApi.customConfig,
            headers: originalApi.headers,
            metadata: originalApi.metadata,
            tags: originalApi.tags,
          },
        });

        await auditLog({
          session: ctx.session,
          resourceType: "apiManagement",
          resourceId: copiedApi.id,
          action: "create",
          after: copiedApi,
          before: null,
        });

        return copiedApi;
      } catch (error) {
        console.error("复制API配置失败:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "复制API配置失败",
        });
      }
    }),

  // 获取模型列表
  getModels: protectedProjectProcedure
    .input(
      z.object({
        projectId: z.string(),
        baseUrl: z.string().min(1, "API地址不能为空"),
        authKey: z.string().min(1, "认证密钥不能为空"),
        type: z.enum(["openai_compatible"]).default("openai_compatible"),
      }),
    )
    .mutation(async ({ input }) => {
      try {
        if (input.type === "openai_compatible") {
          // 创建AbortController用于超时控制
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

          try {
            const response = await fetch(`${input.baseUrl}/models`, {
              method: "GET",
              headers: {
                Authorization: `Bearer ${input.authKey}`,
                "Content-Type": "application/json",
              },
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
              throw new Error(
                `获取模型列表失败: ${response.status} ${response.statusText}`,
              );
            }

            const data = await response.json();

            // 解析模型列表
            let modelsArray: any[] = [];
            if (data.data && Array.isArray(data.data)) {
              modelsArray = data.data;
            } else if (data.items && Array.isArray(data.items)) {
              modelsArray = data.items;
            } else if (Array.isArray(data)) {
              modelsArray = data;
            } else {
              throw new Error("API返回格式不符合OpenAI兼容接口规范");
            }

            // 提取模型名称和信息
            const models = modelsArray
              .map((model: any) => ({
                id: model.name || model.id || model.model,
                name: model.name || model.id || model.model,
                description: model.description || "",
                created: model.created || null,
                owned_by: model.owned_by || model.owner || "",
              }))
              .filter((model) => model.id); // 过滤掉没有ID的模型

            return {
              success: true,
              data: models,
              total: models.length,
            };
          } catch (error: any) {
            clearTimeout(timeoutId);

            if (error.name === "AbortError") {
              throw new Error("连接超时，请检查网络连接和API地址");
            }

            if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
              throw new Error(
                `无法连接到API服务器 ${input.baseUrl}，请检查地址是否正确`,
              );
            }

            throw error;
          }
        } else {
          throw new Error("暂不支持此类型的API模型列表获取");
        }
      } catch (error: any) {
        console.error("获取模型列表失败:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "获取模型列表失败",
        });
      }
    }),

  // 测试API连接
  testConnection: protectedProjectProcedure
    .input(TestApiConnectionSchema)
    .mutation(async ({ input, ctx }) => {
      // 临时注释权限检查用于开发测试
      // throwIfNoProjectAccess({
      //   session: ctx.session,
      //   projectId: input.projectId,
      //   scope: "apiManagement:read",
      // });

      const api = await ctx.prisma.apiManagement.findFirst({
        where: {
          id: input.apiId,
          projectId: input.projectId,
        },
      });

      if (!api) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "API配置未找到",
        });
      }

      try {
        let testResult = { success: false, message: "", data: null as any };

        switch (api.type) {
          case "openai_compatible":
            testResult = await testOpenAICompatibleAPI(api);
            break;
          case "ragflow_agent":
            testResult = await testRagflowAgentAPI(api);
            break;
          case "dify":
            testResult = await testDifyAPI(api);
            break;
          case "other":
            testResult = await testOtherAPI(api);
            break;
          default:
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "不支持的API类型",
            });
        }

        return testResult;
      } catch (error) {
        console.error("测试API连接失败:", error);
        return {
          success: false,
          message: error instanceof Error ? error.message : "连接测试失败",
          data: null,
        };
      }
    }),
});

// 测试OpenAI兼容接口
async function testOpenAICompatibleAPI(api: any) {
  if (!api.baseUrl || !api.authKey) {
    throw new Error("缺少必要的配置参数：baseUrl 和 authKey");
  }

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

  try {
    // 第一步：获取模型列表验证连接
    const modelsResponse = await fetch(`${api.baseUrl}/models`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${api.authKey}`,
        "Content-Type": "application/json",
      },
      signal: controller.signal,
    });

    if (!modelsResponse.ok) {
      let errorMessage = `获取模型列表失败: ${modelsResponse.status} ${modelsResponse.statusText}`;

      try {
        const errorData = await modelsResponse.text();
        if (errorData) {
          errorMessage += ` - ${errorData}`;
        }
      } catch (e) {
        // 忽略解析错误的异常
      }

      throw new Error(errorMessage);
    }

    const modelsData = await modelsResponse.json();

    // 验证返回数据格式并获取模型列表
    if (!modelsData || typeof modelsData !== "object") {
      throw new Error("API返回数据格式不正确");
    }

    let modelsArray: any[] = [];
    if (modelsData.data && Array.isArray(modelsData.data)) {
      modelsArray = modelsData.data;
    } else if (modelsData.items && Array.isArray(modelsData.items)) {
      modelsArray = modelsData.items;
    } else if (Array.isArray(modelsData)) {
      modelsArray = modelsData;
    } else {
      throw new Error(
        "API返回格式不符合OpenAI兼容接口规范，未找到模型列表数据",
      );
    }

    // 第二步：如果指定了模型名称，验证该模型是否可用
    if (api.modelName) {
      const modelExists = modelsArray.some((model: any) => {
        // 支持不同的模型名称字段，优先使用name字段
        const modelName = model.name || model.id || model.model;
        return modelName === api.modelName;
      });

      if (!modelExists) {
        const availableModels = modelsArray
          .map((model: any) => model.name || model.id || model.model)
          .filter(Boolean)
          .slice(0, 5); // 显示前5个可用模型

        throw new Error(
          `指定的模型 "${api.modelName}" 不存在。可用模型包括: ${availableModels.join(", ")}${modelsArray.length > 5 ? " 等" : ""}`,
        );
      }

      // 第三步：尝试使用指定模型进行简单的API调用测试
      try {
        const testResponse = await fetch(`${api.baseUrl}/chat/completions`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${api.authKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model: api.modelName,
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1,
            temperature: 0,
          }),
          signal: controller.signal,
        });

        // 即使API调用失败，只要不是认证或模型不存在的错误，也认为配置有效
        if (!testResponse.ok && testResponse.status === 401) {
          throw new Error("API密钥无效或已过期");
        }

        clearTimeout(timeoutId);

        return {
          success: true,
          message: `连接成功 - 模型 "${api.modelName}" 可用 (共发现 ${modelsArray.length} 个模型)`,
          data: {
            models: modelsData,
            testedModel: api.modelName,
            totalModels: modelsArray.length,
          },
        };
      } catch (testError: any) {
        // 如果是超时或网络错误，抛出异常
        if (testError.name === "AbortError" || testError.code === "ENOTFOUND") {
          throw testError;
        }

        // 其他错误（如模型调用失败）不影响连接测试结果
        clearTimeout(timeoutId);

        return {
          success: true,
          message: `连接成功 - 模型 "${api.modelName}" 已验证存在 (共发现 ${modelsArray.length} 个模型)`,
          data: {
            models: modelsData,
            testedModel: api.modelName,
            totalModels: modelsArray.length,
            note: "模型存在但调用测试未完成",
          },
        };
      }
    } else {
      // 没有指定模型名称，只验证连接和获取模型列表
      clearTimeout(timeoutId);

      return {
        success: true,
        message: `连接成功 - ${api.baseUrl} (发现 ${modelsArray.length} 个可用模型)`,
        data: {
          models: modelsData,
          totalModels: modelsArray.length,
        },
      };
    }
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (error.name === "AbortError") {
      throw new Error("连接超时，请检查网络连接和API地址");
    }

    if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
      throw new Error(`无法连接到API服务器 ${api.baseUrl}，请检查地址是否正确`);
    }

    throw error;
  }
}

// 测试Ragflow智能体接口
async function testRagflowAgentAPI(api: any) {
  if (!api.address || !api.apiKey || !api.agentId) {
    throw new Error("缺少必要的配置参数：address、apiKey 和 agentId");
  }

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

  try {
    // 第一步：创建session
    const sessionResponse = await fetch(
      `${api.address}/api/v1/agents/${api.agentId}/sessions`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${api.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          question: "测试连接",
          stream: false,
        }),
        signal: controller.signal,
      },
    );

    clearTimeout(timeoutId);

    if (!sessionResponse.ok) {
      let errorMessage = `创建会话失败: ${sessionResponse.status} ${sessionResponse.statusText}`;

      // 尝试获取更详细的错误信息
      try {
        const errorData = await sessionResponse.text();
        if (errorData) {
          errorMessage += ` - ${errorData}`;
        }
      } catch (e) {
        // 忽略解析错误的异常
      }

      throw new Error(errorMessage);
    }

    const sessionData = await sessionResponse.json();

    if (sessionData.code !== 0) {
      throw new Error(`创建会话失败: ${sessionData.message || "未知错误"}`);
    }

    return {
      success: true,
      message: `Ragflow连接成功 - ${api.address}`,
      data: {
        sessionId: sessionData.data.session_id,
        answer: sessionData.data.answer,
        agentId: api.agentId,
      },
    };
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (error.name === "AbortError") {
      throw new Error("连接超时，请检查网络连接和API地址");
    }

    // 如果是网络错误，提供更友好的错误信息
    if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
      throw new Error(
        `无法连接到Ragflow服务器 ${api.address}，请检查地址是否正确`,
      );
    }

    throw error;
  }
}

// 测试Dify接口
async function testDifyAPI(api: any) {
  if (!api.difyBaseUrl || !api.difyApiKey) {
    throw new Error("缺少必要的配置参数：difyBaseUrl 和 difyApiKey");
  }

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

  try {
    // 测试获取应用信息
    const response = await fetch(`${api.difyBaseUrl}/v1/parameters`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${api.difyApiKey}`,
        "Content-Type": "application/json",
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = `API请求失败: ${response.status} ${response.statusText}`;

      // 尝试获取更详细的错误信息
      try {
        const errorData = await response.text();
        if (errorData) {
          errorMessage += ` - ${errorData}`;
        }
      } catch (e) {
        // 忽略解析错误的异常
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();

    // 验证Dify响应格式
    if (!data || typeof data !== "object") {
      throw new Error("Dify API返回数据格式不正确");
    }

    return {
      success: true,
      message: `Dify连接成功 - ${api.difyBaseUrl}`,
      data: data,
    };
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (error.name === "AbortError") {
      throw new Error("连接超时，请检查网络连接和API地址");
    }

    // 如果是网络错误，提供更友好的错误信息
    if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
      throw new Error(
        `无法连接到Dify服务器 ${api.difyBaseUrl}，请检查地址是否正确`,
      );
    }

    throw error;
  }
}

// 测试其他类型接口
async function testOtherAPI(api: any) {
  if (!api.customConfig || !api.customConfig.testUrl) {
    throw new Error("缺少测试URL配置");
  }

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...api.headers,
  };

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

  try {
    const response = await fetch(api.customConfig.testUrl, {
      method: api.customConfig.testMethod || "GET",
      headers,
      signal: controller.signal,
      ...(api.customConfig.testBody && {
        body: JSON.stringify(api.customConfig.testBody),
      }),
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = `API请求失败: ${response.status} ${response.statusText}`;

      // 尝试获取更详细的错误信息
      try {
        const errorData = await response.text();
        if (errorData) {
          errorMessage += ` - ${errorData}`;
        }
      } catch (e) {
        // 忽略解析错误的异常
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();

    // 验证响应格式
    if (!data || typeof data !== "object") {
      throw new Error("自定义API返回数据格式不正确");
    }

    return {
      success: true,
      message: `自定义API连接成功 - ${api.customConfig.testUrl}`,
      data: data,
    };
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (error.name === "AbortError") {
      throw new Error("连接超时，请检查网络连接和API地址");
    }

    // 如果是网络错误，提供更友好的错误信息
    if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
      throw new Error(
        `无法连接到自定义API服务器 ${api.customConfig.testUrl}，请检查地址是否正确`,
      );
    }

    throw error;
  }
}
