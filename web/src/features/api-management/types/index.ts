export type ApiType = "openai_compatible" | "ragflow_agent" | "dify" | "other";

export type ApiStatus = "active" | "inactive" | "testing" | "deprecated";

export interface ApiManagement {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  projectId: string;
  name: string;
  description?: string;
  type: ApiType;
  status: ApiStatus;

  // OpenAI Compatible API fields
  baseUrl?: string;
  modelName?: string;
  authKey?: string;

  // Ragflow Agent API fields
  agentId?: string;
  address?: string;
  apiKey?: string;

  // Dify API fields
  difyBaseUrl?: string;
  difyApiKey?: string;
  difyAppId?: string;

  // Other API fields
  customConfig?: Record<string, any>;
  headers?: Record<string, string>;

  // Metadata
  metadata?: Record<string, any>;
  tags: string[];
}

export interface ApiManagementListResponse {
  data: ApiManagement[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiTestResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface OpenAICompatibleConfig {
  baseUrl: string;
  modelName?: string;
  authKey: string;
}

export interface RagflowAgentConfig {
  agentId: string;
  address: string;
  apiKey: string;
}

export interface DifyConfig {
  difyBaseUrl: string;
  difyApiKey: string;
  difyAppId?: string;
}

export interface OtherApiConfig {
  customConfig: {
    testUrl: string;
    testMethod?: string;
    testBody?: any;
  };
  headers?: Record<string, string>;
}

export const API_TYPE_LABELS: Record<ApiType, string> = {
  openai_compatible: "OpenAI兼容接口",
  ragflow_agent: "Ragflow智能体",
  dify: "Dify接口",
  other: "其他类型",
};

export const API_STATUS_LABELS: Record<ApiStatus, string> = {
  active: "活跃",
  inactive: "非活跃",
  testing: "测试中",
  deprecated: "已弃用",
};

export const API_STATUS_COLORS: Record<ApiStatus, string> = {
  active: "bg-green-100 text-green-800",
  inactive: "bg-gray-100 text-gray-800",
  testing: "bg-yellow-100 text-yellow-800",
  deprecated: "bg-red-100 text-red-800",
};
