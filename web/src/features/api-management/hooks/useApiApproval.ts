import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// API状态枚举
export enum ApiStatus {
  PENDING = "pending",
  ACTIVE = "active",
  REJECTED = "rejected",
  INACTIVE = "inactive",
  TESTING = "testing",
  DEPRECATED = "deprecated",
}

// API类型枚举
export enum ApiType {
  OPENAI_COMPATIBLE = "openai_compatible",
  RAGFLOW_AGENT = "ragflow_agent",
  DIFY = "dify",
  OTHER = "other",
}

// API状态选项
export const API_STATUS_OPTIONS = [
  { value: ApiStatus.PENDING, label: "待审批", color: "blue" },
  { value: ApiStatus.ACTIVE, label: "已激活", color: "green" },
  { value: ApiStatus.REJECTED, label: "已拒绝", color: "red" },
  { value: ApiStatus.INACTIVE, label: "未激活", color: "gray" },
  { value: ApiStatus.TESTING, label: "测试中", color: "yellow" },
  { value: ApiStatus.DEPRECATED, label: "已废弃", color: "gray" },
];

// API类型选项
export const API_TYPE_OPTIONS = [
  { value: ApiType.OPENAI_COMPATIBLE, label: "OpenAI兼容", icon: "🤖" },
  { value: ApiType.RAGFLOW_AGENT, label: "RAGFlow代理", icon: "🔄" },
  { value: ApiType.DIFY, label: "Dify", icon: "🚀" },
  { value: ApiType.OTHER, label: "其他", icon: "🔌" },
];

// 获取API状态颜色
export function getApiStatusColor(status: ApiStatus): string {
  const option = API_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.color || "gray";
}

// 获取API状态标签
export function getApiStatusLabel(status: ApiStatus): string {
  const option = API_STATUS_OPTIONS.find((opt) => opt.value === status);
  return option?.label || status;
}

// 获取API类型标签
export function getApiTypeLabel(type: ApiType): string {
  const option = API_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.label || type;
}

// 获取API类型图标
export function getApiTypeIcon(type: ApiType): string {
  const option = API_TYPE_OPTIONS.find((opt) => opt.value === type);
  return option?.icon || "🔌";
}

// 使用API列表
export function useApiList(
  projectId: string,
  filters?: {
    status?: ApiStatus;
    type?: ApiType;
    search?: string;
  },
) {
  return api.apiManagement.list.useQuery(
    {
      projectId,
      ...filters,
    },
    {
      enabled: !!projectId,
      refetchOnWindowFocus: false,
    },
  );
}

// 使用API统计
export function useApiStats(projectId: string) {
  return api.apiManagement.stats.useQuery(
    { projectId },
    {
      enabled: !!projectId,
      refetchOnWindowFocus: false,
    },
  );
}

// 使用批量API审批
export function useBatchApproveApis() {
  const queryClient = useQueryClient();

  return api.apiManagement.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      const isMultiple = variables.apiIds.length > 1;
      const statusMap: Record<string, string> = {
        active: "激活",
        inactive: "停用",
        testing: "设为测试",
        deprecated: "设为弃用",
      };

      const statusText = variables.status
        ? statusMap[variables.status]
        : variables.approved
          ? "激活"
          : "停用";

      toast.success(
        isMultiple ? `批量${statusText}成功` : `API${statusText}成功`,
        {
          description: isMultiple
            ? `已${statusText} ${variables.apiIds.length} 个API`
            : `API状态已更新为${statusText}`,
        },
      );

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("批量操作失败", {
        description: error.message,
      });
    },
  });
}

// 使用创建API
export function useCreateApi() {
  const queryClient = useQueryClient();

  return api.apiManagement.create.useMutation({
    onSuccess: (data) => {
      toast.success("API创建成功", {
        description: `API "${data.name}" 已创建，等待审批`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("创建API失败", {
        description: error.message,
      });
    },
  });
}

// 使用更新API
export function useUpdateApi() {
  const queryClient = useQueryClient();

  return api.apiManagement.update.useMutation({
    onSuccess: (data) => {
      toast.success("API更新成功", {
        description: `API "${data.name}" 已更新`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "byId"]],
      });
    },
    onError: (error) => {
      toast.error("更新API失败", {
        description: error.message,
      });
    },
  });
}

// 使用删除API
export function useDeleteApi() {
  const queryClient = useQueryClient();

  return api.apiManagement.delete.useMutation({
    onSuccess: () => {
      toast.success("API删除成功");

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("删除API失败", {
        description: error.message,
      });
    },
  });
}

// 使用单个API状态更新（专门的单个操作Hook）
export function useUpdateApiStatus() {
  const queryClient = useQueryClient();

  return api.apiManagement.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      // 根据实际状态显示准确的提示信息
      const statusMap: Record<string, string> = {
        active: "激活",
        inactive: "停用",
        testing: "设为测试",
        deprecated: "设为弃用",
      };

      const statusText = variables.status
        ? statusMap[variables.status]
        : variables.approved
          ? "激活"
          : "停用";

      toast.success(`API${statusText}成功`, {
        description: `API状态已更新为${statusText}`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("状态更新失败", {
        description: error.message,
      });
    },
  });
}

// 使用单个API审批（通过批量接口实现，保持向后兼容）
export function useApproveApi() {
  const queryClient = useQueryClient();

  return api.apiManagement.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      toast.success(variables.approved ? "API审批通过" : "API审批拒绝", {
        description: `API已${variables.approved ? "激活" : "拒绝"}`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["apiManagement", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("操作失败", {
        description: error.message,
      });
    },
  });
}
