import { api } from "@/src/utils/api";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type {
  CreateApiManagementInput,
  UpdateApiManagementInput,
  ListApiManagementInput,
  GetApiManagementInput,
  DeleteApiManagementInput,
  TestApiConnectionInput,
  CopyApiManagementInput,
} from "../server/apiManagementRouter";

// 获取API统计信息
export const useApiStats = (projectId: string) => {
  return api.apiManagement.stats.useQuery(
    { projectId },
    {
      refetchInterval: 30000, // 每30秒刷新一次
    },
  );
};

// 获取API列表
export const useApiManagementList = (input: ListApiManagementInput) => {
  return api.apiManagement.list.useQuery(input, {
    keepPreviousData: true,
  });
};

// 获取单个API配置
export const useApiManagement = (input: GetApiManagementInput) => {
  return api.apiManagement.byId.useQuery(input, {
    enabled: !!input.apiId && !!input.projectId,
  });
};

// 创建API配置
export const useCreateApiManagement = () => {
  const queryClient = useQueryClient();

  return api.apiManagement.create.useMutation({
    onSuccess: (data, variables) => {
      toast.success("API配置创建成功");

      // 刷新列表
      queryClient.invalidateQueries({
        queryKey: [
          ["apiManagement", "list"],
          { input: { projectId: variables.projectId } },
        ],
      });
    },
    onError: (error) => {
      console.error("创建API配置失败:", error);
      toast.error(error.message || "创建API配置失败");
    },
  });
};

// 更新API配置
export const useUpdateApiManagement = () => {
  const queryClient = useQueryClient();

  return api.apiManagement.update.useMutation({
    onSuccess: (data, variables) => {
      toast.success("API配置更新成功");

      // 刷新列表和详情
      queryClient.invalidateQueries({
        queryKey: [
          ["apiManagement", "list"],
          { input: { projectId: variables.projectId } },
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          ["apiManagement", "byId"],
          { input: { apiId: variables.apiId, projectId: variables.projectId } },
        ],
      });
    },
    onError: (error) => {
      console.error("更新API配置失败:", error);
      toast.error(error.message || "更新API配置失败");
    },
  });
};

// 删除API配置
export const useDeleteApiManagement = () => {
  const queryClient = useQueryClient();

  return api.apiManagement.delete.useMutation({
    onSuccess: (data, variables) => {
      toast.success("API配置删除成功");

      // 刷新列表
      queryClient.invalidateQueries({
        queryKey: [
          ["apiManagement", "list"],
          { input: { projectId: variables.projectId } },
        ],
      });
    },
    onError: (error) => {
      console.error("删除API配置失败:", error);
      toast.error(error.message || "删除API配置失败");
    },
  });
};

// 复制API配置
export const useCopyApiManagement = () => {
  const queryClient = useQueryClient();

  return api.apiManagement.copy.useMutation({
    onSuccess: (data, variables) => {
      toast.success("API配置复制成功");

      // 刷新列表
      queryClient.invalidateQueries({
        queryKey: [
          ["apiManagement", "list"],
          { input: { projectId: variables.projectId } },
        ],
      });

      // 刷新统计信息
      queryClient.invalidateQueries({
        queryKey: [
          ["apiManagement", "stats"],
          { input: { projectId: variables.projectId } },
        ],
      });
    },
    onError: (error) => {
      console.error("复制API配置失败:", error);
      toast.error(error.message || "复制API配置失败");
    },
  });
};

// 获取模型列表
export const useGetModels = () => {
  return api.apiManagement.getModels.useMutation({
    onError: (error) => {
      console.error("获取模型列表失败:", error);
      toast.error(error.message || "获取模型列表失败");
    },
  });
};

// 测试API连接
export const useTestApiConnection = () => {
  return api.apiManagement.testConnection.useMutation({
    onSuccess: (data) => {
      if (data.success) {
        // 显示成功信息和详细数据
        let message = data.message || "连接测试成功";
        if (data.data) {
          // 根据不同API类型显示不同的详细信息
          if (data.data.data && Array.isArray(data.data.data)) {
            message += ` (发现 ${data.data.data.length} 个模型)`;
          } else if (data.data.sessionId) {
            message += ` (会话ID: ${data.data.sessionId.substring(0, 8)}...)`;
          } else if (data.data.answer) {
            message += ` (响应: ${data.data.answer.substring(0, 20)}...)`;
          }
        }
        toast.success(message);
      } else {
        toast.error(data.message || "连接测试失败");
      }
    },
    onError: (error) => {
      console.error("测试API连接失败:", error);
      toast.error(error.message || "连接测试失败");
    },
  });
};
