// i18n 监控仪表板组件
// i18n Monitoring Dashboard Component

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { Button } from "@/src/components/ui/button";

interface HealthStatus {
  status: "healthy" | "unhealthy";
  timestamp: string;
  checks: Record<string, { status: "pass" | "fail"; details: any }>;
  metrics: {
    totalTranslationFiles: number;
    totalTranslationKeys: number;
    supportedLanguages: string[];
  };
}

export function I18nMonitoringDashboard() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const fetchHealthStatus = async () => {
    try {
      const response = await fetch("/api/health/i18n");
      const data = await response.json();
      setHealthStatus(data);
      setLastUpdate(new Date());
    } catch (error) {
      console.error("Failed to fetch health status:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthStatus();
    const interval = setInterval(fetchHealthStatus, 30000); // 每30秒更新
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return <div className="p-4">加载中...</div>;
  }

  if (!healthStatus) {
    return <div className="p-4 text-red-500">无法加载监控数据</div>;
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">国际化监控仪表板</h1>
        <div className="flex items-center space-x-2">
          <Badge
            variant={
              healthStatus.status === "healthy" ? "default" : "destructive"
            }
          >
            {healthStatus.status === "healthy" ? "健康" : "异常"}
          </Badge>
          <Button onClick={fetchHealthStatus} size="sm">
            刷新
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>翻译文件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {healthStatus.metrics.totalTranslationFiles}
            </div>
            <p className="text-sm text-muted-foreground">总文件数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>翻译键</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {healthStatus.metrics.totalTranslationKeys}
            </div>
            <p className="text-sm text-muted-foreground">总键数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>支持语言</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {healthStatus.metrics.supportedLanguages.length}
            </div>
            <p className="text-sm text-muted-foreground">
              {healthStatus.metrics.supportedLanguages.join(", ")}
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>系统检查</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(healthStatus.checks).map(([key, check]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="capitalize">
                  {key.replace(/([A-Z])/g, " $1")}
                </span>
                <Badge
                  variant={check.status === "pass" ? "default" : "destructive"}
                >
                  {check.status === "pass" ? "通过" : "失败"}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="text-sm text-muted-foreground">
        最后更新: {lastUpdate.toLocaleString()}
      </div>
    </div>
  );
}
