import { useState } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Page from "@/src/components/layouts/page";
import { TenantManagementList } from "@/src/features/tenant-management/components/TenantManagementList";
import { TenantStats } from "@/src/features/tenant-management/components/TenantStats";
import { CreateTenantDialog } from "@/src/features/tenant-management/components/CreateTenantDialog";

export default function TenantManagementPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);

  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const handleViewTenant = (tenantId: string) => {
    // 导航到租户详情页面
    router.push(`/project/${projectId}/registration/tenants/${tenantId}`);
  };

  const handleEditTenant = (tenantId: string) => {
    // 导航到租户编辑页面
    router.push(`/project/${projectId}/registration/tenants/${tenantId}/edit`);
  };

  const handleCreateTenant = () => {
    setCreateDialogOpen(true);
  };

  const handleCreateSuccess = (tenant: any) => {
    // 创建成功后的处理
    console.log("租户创建成功:", tenant);
  };

  return (
    <Page
      headerProps={{
        title: t("registration:tenants.title", "租户管理"),
        help: {
          description: t(
            "registration:tenants.description",
            "管理多租户环境，配置租户隔离和资源分配",
          ),
          href: "https://docs.example.com/registration/tenants",
        },
      }}
    >
      <TenantStats projectId={projectId} />
      <TenantManagementList
        onViewTenant={handleViewTenant}
        onEditTenant={handleEditTenant}
        onCreateTenant={handleCreateTenant}
      />

      <CreateTenantDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
