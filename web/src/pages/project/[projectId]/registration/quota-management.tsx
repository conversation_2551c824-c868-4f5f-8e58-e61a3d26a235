import { useState } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Page from "@/src/components/layouts/page";
import { QuotaManagementList } from "@/src/features/quota-management/components/QuotaManagementList";
import { QuotaStats } from "@/src/features/quota-management/components/QuotaStats";
import { CreateQuotaDialog } from "@/src/features/quota-management/components/CreateQuotaDialog";
import { EditQuotaDialog } from "@/src/features/quota-management/components/EditQuotaDialog";
import { useQuotaAllocations } from "@/src/features/quota-management/hooks/useQuotaManagement";

export default function QuotaManagementPage() {
  const { t } = useTranslation(["common", "registration"]);
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editQuotaId, setEditQuotaId] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  // 获取配额列表以获取编辑数据
  const { data: quotaData } = useQuotaAllocations({
    projectId,
    limit: 1000, // 获取所有配额用于编辑
    page: 0,
  });

  const handleCreateQuota = () => {
    setCreateDialogOpen(true);
  };

  const handleEditQuota = (quotaId: string) => {
    setEditQuotaId(quotaId);
    setEditDialogOpen(true);
  };

  const handleCreateSuccess = () => {
    setCreateDialogOpen(false);
  };

  const handleEditSuccess = () => {
    setEditDialogOpen(false);
    setEditQuotaId(null);
  };

  // 获取当前编辑的配额数据
  const currentQuota = quotaData?.quotas?.find((q) => q.id === editQuotaId);

  return (
    <Page
      headerProps={{
        title: t("registration:quota.title", "配额管理"),
        help: {
          description: t(
            "registration:quota.description",
            "管理租户、应用和API的资源配额分配，监控使用情况并设置告警",
          ),
          href: "https://docs.example.com/registration/quota-management",
        },
      }}
    >
      <QuotaStats projectId={projectId} />
      <QuotaManagementList
        onCreateQuota={handleCreateQuota}
        onEditQuota={handleEditQuota}
      />

      <CreateQuotaDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />

      <EditQuotaDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        quotaId={editQuotaId}
        initialData={
          currentQuota
            ? {
                limit: currentQuota.limit,
                period: currentQuota.period,
                warningThreshold: currentQuota.warningThreshold,
                description: currentQuota.description || "",
                status: currentQuota.status,
              }
            : undefined
        }
        onSuccess={handleEditSuccess}
      />
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
