import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { api } from "@/src/utils/api";
import Page from "@/src/components/layouts/page";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { Checkbox } from "@/src/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/src/components/ui/alert-dialog";
import { Textarea } from "@/src/components/ui/textarea";
import { Label } from "@/src/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import {
  Plus,
  AppWindow,
  Settings,
  Trash2,
  Bot,
  FileText,
  Stethoscope,
  MessageSquare,
  Building2,
  Eye,
  Edit,
  Check,
  X,
  RefreshCw,
  MoreHorizontal,
  Pause,
} from "lucide-react";
import { CreateApplicationDialog } from "@/src/features/registration/components/CreateApplicationDialog";
import {
  useApplications,
  useApplicationStats,
  useDeleteApplication,
  useApproveApplication,
  getApplicationStatusInfo,
  getApplicationTypeInfo,
  ApplicationType,
} from "@/src/features/registration/hooks/useApplications";

// 添加批量审批Hook
function useBatchApproveApplications() {
  const queryClient = useQueryClient();

  return api.applications.batchApprove.useMutation({
    onSuccess: (data, variables) => {
      toast.success(variables.approved ? "批量审批通过" : "批量审批拒绝", {
        description: `已${variables.approved ? "激活" : "拒绝"} ${data.processedCount} 个应用`,
      });

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: [["applications", "list"]],
      });
      queryClient.invalidateQueries({
        queryKey: [["applications", "stats"]],
      });
    },
    onError: (error) => {
      toast.error("批量操作失败", {
        description: error.message,
      });
    },
  });
}

export default function ApplicationRegistrationPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // 批量选择状态
  const [selectedApplications, setSelectedApplications] = useState<string[]>(
    [],
  );
  const [selectAll, setSelectAll] = useState(false);

  // 审批对话框状态
  const [approvalDialog, setApprovalDialog] = useState({
    open: false,
    applicationId: null as string | null,
    isBatch: false,
    approved: true,
  });
  const [approvalReason, setApprovalReason] = useState("");

  // 获取应用数据
  const { data: applicationsData, isLoading: applicationsLoading } =
    useApplications({
      projectId,
      limit: 50,
    });

  // 获取统计数据
  const { data: statsData, isLoading: statsLoading } =
    useApplicationStats(projectId);

  // 删除应用功能
  const deleteApplication = useDeleteApplication();

  // 审核应用功能
  const approveApplication = useApproveApplication();
  const batchApproveApplications = useBatchApproveApplications();

  const applications = applicationsData?.applications || [];
  const stats = statsData || {
    totalCount: 0,
    activeCount: 0,
    pendingCount: 0,
    totalUsage: 0,
    activeRate: 0,
  };

  const handleDeleteApplication = async (applicationId: string) => {
    if (confirm("确定要删除这个应用吗？此操作无法撤销。")) {
      try {
        await deleteApplication.mutateAsync({
          projectId,
          applicationId,
        });
      } catch (error) {
        console.error("删除应用失败:", error);
      }
    }
  };

  // 单个应用状态更新（参考配额管理逻辑，无需确认直接更新）
  const handleSingleStatusUpdate = async (
    applicationId: string,
    status: string,
  ) => {
    try {
      const approved = status === "ACTIVE";
      await approveApplication.mutateAsync({
        projectId,
        applicationId,
        approved,
        status: status as any, // 传递具体状态
        reason: undefined, // 快速操作不需要原因
      });
    } catch (error) {
      console.error("更新应用状态失败:", error);
    }
  };

  // 批量状态更新
  const handleBatchStatusUpdate = async (status: string) => {
    if (selectedApplications.length === 0) return;

    const statusLabels: Record<string, string> = {
      ACTIVE: "激活",
      REJECTED: "拒绝",
      SUSPENDED: "暂停",
    };

    if (
      confirm(
        `确定要将选中的 ${selectedApplications.length} 个应用设置为${statusLabels[status]}状态吗？`,
      )
    ) {
      try {
        // 使用批量审批接口，现在支持任意状态切换
        await batchApproveApplications.mutateAsync({
          projectId,
          applicationIds: selectedApplications,
          approved: status === "ACTIVE",
          status: status as any, // 传递具体状态
          reason: undefined,
        });
        setSelectedApplications([]);
        setSelectAll(false);
        toast.success(
          `已成功${statusLabels[status]} ${selectedApplications.length} 个应用`,
        );
      } catch (error) {
        console.error("批量操作失败:", error);
      }
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedApplications(applications.map((app) => app.id));
    } else {
      setSelectedApplications([]);
    }
  };

  // 处理单选
  const handleSelectApplication = (applicationId: string, checked: boolean) => {
    if (checked) {
      setSelectedApplications((prev) => [...prev, applicationId]);
    } else {
      setSelectedApplications((prev) =>
        prev.filter((id) => id !== applicationId),
      );
      setSelectAll(false);
    }
  };

  // 处理审批
  const handleApproval = async () => {
    try {
      if (approvalDialog.isBatch) {
        await batchApproveApplications.mutateAsync({
          projectId,
          applicationIds: selectedApplications,
          approved: approvalDialog.approved,
          reason: approvalReason,
        });
        setSelectedApplications([]);
        setSelectAll(false);
      } else if (approvalDialog.applicationId) {
        await approveApplication.mutateAsync({
          projectId,
          applicationId: approvalDialog.applicationId,
          approved: approvalDialog.approved,
          reason: approvalReason,
        });
      }

      setApprovalDialog({
        open: false,
        applicationId: null,
        isBatch: false,
        approved: true,
      });
      setApprovalReason("");
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  // 查看应用详情
  const handleViewApplication = (applicationId: string) => {
    router.push(
      `/project/${projectId}/registration/applications/${applicationId}`,
    );
  };

  // 编辑应用
  const handleEditApplication = (applicationId: string) => {
    router.push(
      `/project/${projectId}/registration/applications/${applicationId}?tab=edit`,
    );
  };

  // 应用设置
  const handleApplicationSettings = (applicationId: string) => {
    router.push(
      `/project/${projectId}/registration/applications/${applicationId}?tab=settings`,
    );
  };

  // 应用类型图标映射
  const getAppIcon = (type: string) => {
    const iconMap: Record<string, any> = {
      "robot-application": Bot,
      "quality-control-app": FileText,
      "document-generation-app": Stethoscope,
      "intelligent-customer-service": MessageSquare,
      "intelligent-agent-app": Building2,
      "custom-application": AppWindow,
      default: AppWindow,
    };
    return iconMap[type] || iconMap.default;
  };

  const handleCreateSuccess = (newApp: any) => {
    console.log("New application created:", newApp);
    // 数据会通过React Query自动刷新
  };

  return (
    <Page
      headerProps={{
        title: t("applicationRegistration", "Application Registration"),
        help: {
          description: "管理和注册应用程序，配置客户端凭据和权限。",
          href: "https://docs.example.com/registration/applications",
        },
        actionButtonsRight: (
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            新建应用
          </Button>
        ),
      }}
    >
      <div className="space-y-6">
        {/* 概览卡片 */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总应用数</CardTitle>
              <AppWindow className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCount}</div>
              <p className="text-xs text-muted-foreground">
                {statsLoading ? "加载中..." : "总应用数量"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃应用</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeCount}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeRate}% 活跃率
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总使用量</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.totalUsage.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">累计调用次数</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">待审核</CardTitle>
              <Plus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pendingCount}</div>
              <p className="text-xs text-muted-foreground">等待审核应用</p>
            </CardContent>
          </Card>
        </div>

        {/* 批量操作提示 */}
        {selectedApplications.length > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium">
                    已选择 {selectedApplications.length} 个应用
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedApplications([]);
                      setSelectAll(false);
                    }}
                  >
                    取消选择
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBatchStatusUpdate("ACTIVE")}
                    className="text-green-600 hover:bg-green-50 hover:text-green-700"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    批量激活
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBatchStatusUpdate("REJECTED")}
                    className="text-red-600 hover:bg-red-50 hover:text-red-700"
                  >
                    <X className="mr-2 h-4 w-4" />
                    批量拒绝
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBatchStatusUpdate("SUSPENDED")}
                    className="text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                  >
                    <Pause className="mr-2 h-4 w-4" />
                    批量暂停
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 应用列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Checkbox checked={selectAll} onCheckedChange={handleSelectAll} />
              已注册应用
            </CardTitle>
            <CardDescription>管理您的应用程序注册和配置</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="max-h-[600px] overflow-y-auto">
              <div className="space-y-4">
                {applicationsLoading ? (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">加载中...</p>
                  </div>
                ) : applications.length === 0 ? (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">
                      暂无应用，点击&quot;新建应用&quot;开始创建
                    </p>
                  </div>
                ) : (
                  applications.map((app) => {
                    const AppIcon = getAppIcon(app.type);
                    const statusInfo = getApplicationStatusInfo(app.status);
                    const typeInfo = getApplicationTypeInfo(app.type);
                    return (
                      <div
                        key={app.id}
                        className="flex items-start justify-between rounded-lg border p-6 transition-shadow hover:shadow-md"
                      >
                        <div className="flex flex-1 items-start space-x-4">
                          <Checkbox
                            checked={selectedApplications.includes(app.id)}
                            onCheckedChange={(checked) =>
                              handleSelectApplication(
                                app.id,
                                checked as boolean,
                              )
                            }
                            className="mt-2"
                          />
                          <AppIcon className="mt-1 h-10 w-10 text-primary" />
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center justify-between">
                              <h3 className="text-lg font-semibold">
                                {app.name}
                              </h3>
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline">{app.category}</Badge>
                                <span
                                  className={`rounded-full px-2 py-1 text-xs ${
                                    statusInfo.color === "green"
                                      ? "bg-green-100 text-green-800"
                                      : statusInfo.color === "yellow"
                                        ? "bg-yellow-100 text-yellow-800"
                                        : statusInfo.color === "red"
                                          ? "bg-red-100 text-red-800"
                                          : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {statusInfo.label}
                                </span>
                              </div>
                            </div>

                            <p className="text-sm text-muted-foreground">
                              {app.description}
                            </p>

                            <div className="mb-2 flex flex-wrap gap-1">
                              {app.tags.map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground md:grid-cols-4">
                              <div>
                                <span className="font-medium">版本:</span>{" "}
                                {app.version}
                              </div>
                              <div>
                                <span className="font-medium">开发商:</span>{" "}
                                {app.developer}
                              </div>
                              <div>
                                <span className="font-medium">使用量:</span>{" "}
                                {app.usageCount.toLocaleString()}
                              </div>
                              <div>
                                <span className="font-medium">客户端ID:</span>{" "}
                                {app.clientId}
                              </div>
                            </div>

                            <div className="flex items-center justify-between border-t pt-2 text-xs text-muted-foreground">
                              <span>
                                创建时间:{" "}
                                {new Date(app.createdAt).toLocaleDateString()}
                              </span>
                              <span>
                                最后更新:{" "}
                                {new Date(app.updatedAt).toLocaleDateString()}
                              </span>
                              {app.isPublic && (
                                <Badge variant="outline" className="text-xs">
                                  公开应用
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="ml-4 flex items-center gap-1">
                          {/* 快速状态管理按钮 - 参考配额管理逻辑，所有状态可互相切换 */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              handleSingleStatusUpdate(app.id, "ACTIVE")
                            }
                            className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700"
                            title="激活"
                          >
                            <Check className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              handleSingleStatusUpdate(app.id, "REJECTED")
                            }
                            className="h-7 w-7 p-0 text-red-600 hover:bg-red-50 hover:text-red-700"
                            title="拒绝"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              handleSingleStatusUpdate(app.id, "SUSPENDED")
                            }
                            className="h-7 w-7 p-0 text-orange-600 hover:bg-orange-50 hover:text-orange-700"
                            title="暂停"
                          >
                            <Pause className="h-3 w-3" />
                          </Button>

                          {/* 更多操作菜单 */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-7 w-7 p-0">
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleViewApplication(app.id)}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleEditApplication(app.id)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                编辑应用
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleApplicationSettings(app.id)
                                }
                              >
                                <Settings className="mr-2 h-4 w-4" />
                                应用设置
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteApplication(app.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除应用
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 创建应用弹窗 */}
      <CreateApplicationDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />

      {/* 审批对话框 */}
      <AlertDialog
        open={approvalDialog.open}
        onOpenChange={(open) =>
          setApprovalDialog((prev) => ({ ...prev, open }))
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {approvalDialog.approved ? "批准" : "拒绝"}应用
              {approvalDialog.isBatch && `（${selectedApplications.length}个）`}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {approvalDialog.approved
                ? "确定要批准这些应用吗？批准后应用将被激活。"
                : "确定要拒绝这些应用吗？拒绝后应用将无法使用。"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>处理原因</Label>
              <Textarea
                placeholder="请输入处理原因（可选）"
                value={approvalReason}
                onChange={(e) => setApprovalReason(e.target.value)}
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApproval}
              className={
                approvalDialog.approved
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              确认{approvalDialog.approved ? "批准" : "拒绝"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
