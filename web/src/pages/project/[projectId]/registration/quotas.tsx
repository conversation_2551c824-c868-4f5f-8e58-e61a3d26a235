import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Page from "@/src/components/layouts/page";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Progress } from "@/src/components/ui/progress";
import {
  Plus,
  Gauge,
  TrendingUp,
  AlertTriangle,
  Settings,
  Trash2,
} from "lucide-react";

export default function QuotaManagementPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);

  // 模拟数据 - 实际项目中应该从API获取
  const quotas = [
    {
      id: "quota-1",
      name: "API调用配额",
      description: "每月API调用次数限制",
      type: "api_calls",
      limit: 10000,
      used: 7500,
      period: "monthly",
      status: "warning",
    },
    {
      id: "quota-2",
      name: "存储配额",
      description: "数据存储空间限制",
      type: "storage",
      limit: 100, // GB
      used: 45,
      period: "monthly",
      status: "normal",
    },
    {
      id: "quota-3",
      name: "用户数配额",
      description: "最大用户数限制",
      type: "users",
      limit: 500,
      used: 225,
      period: "monthly",
      status: "normal",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "warning":
        return "text-yellow-600 bg-yellow-100";
      case "danger":
        return "text-red-600 bg-red-100";
      default:
        return "text-green-600 bg-green-100";
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    return Math.round((used / limit) * 100);
  };

  return (
    <Page
      headerProps={{
        title: t("quotaManagement", "Quota Management"),
        help: {
          description: "管理系统资源配额，监控使用情况和设置限制。",
          href: "https://docs.example.com/registration/quotas",
        },
        actionButtonsRight: (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            新建配额
          </Button>
        ),
      }}
    >
      <div className="space-y-6">
        {/* 概览卡片 */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总配额数</CardTitle>
              <Gauge className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{quotas.length}</div>
              <p className="text-xs text-muted-foreground">活跃配额规则</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">警告配额</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {quotas.filter((q) => q.status === "warning").length}
              </div>
              <p className="text-xs text-muted-foreground">需要关注</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均使用率</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(
                  quotas.reduce(
                    (sum, q) => sum + getUsagePercentage(q.used, q.limit),
                    0,
                  ) / quotas.length,
                )}
                %
              </div>
              <p className="text-xs text-muted-foreground">跨所有配额</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">超限配额</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {quotas.filter((q) => q.status === "danger").length}
              </div>
              <p className="text-xs text-muted-foreground">立即处理</p>
            </CardContent>
          </Card>
        </div>

        {/* 配额列表 */}
        <Card>
          <CardHeader>
            <CardTitle>配额规则</CardTitle>
            <CardDescription>监控和管理系统资源使用配额</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {quotas.map((quota) => {
                const percentage = getUsagePercentage(quota.used, quota.limit);
                return (
                  <div
                    key={quota.id}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Gauge className="h-6 w-6 text-primary" />
                          <div>
                            <h3 className="font-medium">{quota.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {quota.description}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`rounded-full px-2 py-1 text-xs ${getStatusColor(quota.status)}`}
                          >
                            {quota.status === "warning"
                              ? "警告"
                              : quota.status === "danger"
                                ? "超限"
                                : "正常"}
                          </span>
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>
                            已使用: {quota.used} / {quota.limit}{" "}
                            {quota.type === "storage" ? "GB" : ""}
                          </span>
                          <span>{percentage}%</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
