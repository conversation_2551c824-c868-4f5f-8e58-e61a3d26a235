import { useRouter } from "next/router";
import { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import Page from "@/src/components/layouts/page";
import { ApiManagementList } from "@/src/features/api-management/components/ApiManagementList";
import { ApiStats } from "@/src/features/api-management/components/ApiStats";

export default function ApiManagementPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);

  return (
    <Page
      headerProps={{
        title: "API管理",
        breadcrumb: [
          { name: "注册管理", href: `/project/${projectId}/registration` },
        ],
        help: {
          description:
            "管理和配置各种类型的API接口，包括OpenAI兼容、Ragflow智能体、Dify等。",
          href: "https://docs.example.com/registration/apis",
        },
      }}
    >
      <ApiStats projectId={projectId} />
      <ApiManagementList projectId={projectId} />
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? "en", [
        "common",
        "registration",
      ])),
    },
  };
};
