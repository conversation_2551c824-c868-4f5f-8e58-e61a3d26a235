import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import type { GetServerSideProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Page from "@/src/components/layouts/page";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { Plus, Shield, Key, Users, Lock, Settings, Trash2 } from "lucide-react";

export default function AuthorizationManagementPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const { t } = useTranslation(["common", "registration"]);

  // 模拟数据 - 实际项目中应该从API获取
  const authorizations = [
    {
      id: "auth-1",
      name: "管理员权限",
      description: "系统管理员完整权限",
      type: "role",
      permissions: ["read", "write", "delete", "admin"],
      assignedUsers: 3,
      status: "active",
      createdAt: "2024-01-10",
    },
    {
      id: "auth-2",
      name: "API访问权限",
      description: "API接口访问权限",
      type: "api",
      permissions: ["api:read", "api:write"],
      assignedUsers: 15,
      status: "active",
      createdAt: "2024-01-15",
    },
    {
      id: "auth-3",
      name: "只读权限",
      description: "仅查看数据权限",
      type: "role",
      permissions: ["read"],
      assignedUsers: 25,
      status: "active",
      createdAt: "2024-01-20",
    },
  ];

  const getPermissionColor = (permission: string) => {
    if (permission.includes("admin") || permission.includes("delete")) {
      return "bg-red-100 text-red-800";
    }
    if (permission.includes("write")) {
      return "bg-yellow-100 text-yellow-800";
    }
    return "bg-green-100 text-green-800";
  };

  const totalUsers = authorizations.reduce(
    (sum, auth) => sum + auth.assignedUsers,
    0,
  );

  return (
    <Page
      headerProps={{
        title: t("authorizationManagement", "Authorization Management"),
        help: {
          description: "管理用户权限和访问控制，配置角色和权限分配。",
          href: "https://docs.example.com/registration/authorizations",
        },
        actionButtonsRight: (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            新建权限
          </Button>
        ),
      }}
    >
      <div className="space-y-6">
        {/* 概览卡片 */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">权限规则</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{authorizations.length}</div>
              <p className="text-xs text-muted-foreground">活跃权限规则</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">授权用户</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUsers}</div>
              <p className="text-xs text-muted-foreground">总授权用户数</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API权限</CardTitle>
              <Key className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {authorizations.filter((auth) => auth.type === "api").length}
              </div>
              <p className="text-xs text-muted-foreground">API访问规则</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">角色权限</CardTitle>
              <Lock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {authorizations.filter((auth) => auth.type === "role").length}
              </div>
              <p className="text-xs text-muted-foreground">角色权限规则</p>
            </CardContent>
          </Card>
        </div>

        {/* 权限列表 */}
        <Card>
          <CardHeader>
            <CardTitle>权限配置</CardTitle>
            <CardDescription>管理用户角色和权限分配</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {authorizations.map((auth) => (
                <div
                  key={auth.id}
                  className="flex items-start justify-between rounded-lg border p-4"
                >
                  <div className="flex flex-1 items-start space-x-4">
                    <Shield className="mt-1 h-8 w-8 text-primary" />
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">{auth.name}</h3>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              auth.type === "api" ? "secondary" : "default"
                            }
                          >
                            {auth.type === "api" ? "API" : "角色"}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {auth.assignedUsers} 用户
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {auth.description}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {auth.permissions.map((permission) => (
                          <Badge
                            key={permission}
                            variant="outline"
                            className={getPermissionColor(permission)}
                          >
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 flex items-center space-x-2">
                    <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
                      活跃
                    </span>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
