import { GetServerSideProps } from "next";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Page from "@/src/components/layouts/page";
import { TenantEdit } from "@/src/features/tenant-management/components/TenantEdit";

export default function TenantEditPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const tenantId = router.query.tenantId as string;
  const { t } = useTranslation(["common", "registration"]);

  const handleBack = () => {
    router.push(`/project/${projectId}/registration/tenants/${tenantId}`);
  };

  const handleSuccess = () => {
    router.push(`/project/${projectId}/registration/tenants/${tenantId}`);
  };

  return (
    <Page
      headerProps={{
        title: t("registration:tenants.title", "编辑租户"),
        help: {
          description: t(
            "registration:tenants.description",
            "编辑租户的信息和配置",
          ),
          href: "https://docs.example.com/registration/tenants",
        },
      }}
    >
      <TenantEdit
        tenantId={tenantId}
        projectId={projectId}
        onBack={handleBack}
        onSuccess={handleSuccess}
      />
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
