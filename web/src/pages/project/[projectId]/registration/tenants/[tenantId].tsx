import { GetServerSideProps } from "next";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import Page from "@/src/components/layouts/page";
import { TenantDetails } from "@/src/features/tenant-management/components/TenantDetails";

export default function TenantDetailsPage() {
  const router = useRouter();
  const projectId = router.query.projectId as string;
  const tenantId = router.query.tenantId as string;
  const { t } = useTranslation(["common", "registration"]);

  const handleBack = () => {
    router.push(`/project/${projectId}/registration/tenants`);
  };

  const handleEdit = () => {
    router.push(`/project/${projectId}/registration/tenants/${tenantId}/edit`);
  };

  return (
    <Page
      headerProps={{
        title: t("registration:tenants.title", "租户详情"),
        help: {
          description: t(
            "registration:tenants.description",
            "查看租户的详细信息和配置",
          ),
          href: "https://docs.example.com/registration/tenants",
        },
      }}
    >
      <TenantDetails
        tenantId={tenantId}
        projectId={projectId}
        onBack={handleBack}
        onEdit={handleEdit}
      />
    </Page>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {
      ...(await serverSideTranslations(context.locale ?? "zh", [
        "common",
        "registration",
      ])),
    },
  };
};
