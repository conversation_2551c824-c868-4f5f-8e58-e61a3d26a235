-- Create enums if they don't exist
DO $$ BEGIN
    CREATE TYPE "TenantType" AS ENUM ('enterprise', 'standard', 'basic');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "TenantStatus" AS ENUM ('active', 'inactive', 'suspended', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "TenantRole" AS ENUM ('owner', 'admin', 'member', 'viewer');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "ApplicationType" AS ENUM ('web', 'mobile', 'api', 'desktop');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE "ApprovalStepType" AS ENUM ('manual', 'automatic');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create tenants table
CREATE TABLE IF NOT EXISTS "tenants" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" TEXT NOT NULL,
    "display_name" TEXT,
    "description" TEXT,
    "type" "TenantType" NOT NULL DEFAULT 'standard',
    "category" TEXT,
    "contact_name" TEXT,
    "contact_email" TEXT,
    "contact_phone" TEXT,
    "address" TEXT,
    "website" TEXT,
    "license_number" TEXT,
    "tax_id" TEXT,
    "legal_person" TEXT,
    "status" "TenantStatus" NOT NULL DEFAULT 'pending',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,
    "verified_at" TIMESTAMP(3),
    "suspended_at" TIMESTAMP(3),
    "settings" JSONB,
    "metadata" JSONB,
    "max_users" INTEGER,
    "max_projects" INTEGER,
    "max_applications" INTEGER,
    "storage_limit" BIGINT,
    CONSTRAINT "tenants_pkey" PRIMARY KEY ("id")
);

-- Create tenant_organizations table
CREATE TABLE IF NOT EXISTS "tenant_organizations" (
    "id" TEXT NOT NULL,
    "tenant_id" TEXT NOT NULL,
    "org_id" TEXT NOT NULL,
    "role" "TenantRole" NOT NULL DEFAULT 'member',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "tenant_organizations_pkey" PRIMARY KEY ("id")
);

-- Create indexes
CREATE INDEX IF NOT EXISTS "tenants_name_idx" ON "tenants"("name");
CREATE INDEX IF NOT EXISTS "tenants_status_idx" ON "tenants"("status");
CREATE INDEX IF NOT EXISTS "tenants_type_idx" ON "tenants"("type");
CREATE UNIQUE INDEX IF NOT EXISTS "tenant_organizations_tenant_id_org_id_key" ON "tenant_organizations"("tenant_id", "org_id");
CREATE INDEX IF NOT EXISTS "tenant_organizations_org_id_idx" ON "tenant_organizations"("org_id");
CREATE INDEX IF NOT EXISTS "tenant_organizations_tenant_id_idx" ON "tenant_organizations"("tenant_id");

-- Insert some sample data
INSERT INTO "tenants" ("id", "name", "display_name", "description", "type", "status", "contact_name", "contact_email") 
VALUES 
    ('tenant_1', 'demo-tenant', '演示租户', '这是一个演示租户', 'standard', 'active', '张三', '<EMAIL>'),
    ('tenant_2', 'test-tenant', '测试租户', '这是一个测试租户', 'enterprise', 'pending', '李四', '<EMAIL>')
ON CONFLICT ("id") DO NOTHING;
