#!/usr/bin/env node

/**
 * 验证翻译完整性脚本
 * Verify translation completeness script
 */

const fs = require("fs");
const path = require("path");

// 颜色输出
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
  reset: "\x1b[0m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查翻译文件完整性
function checkTranslationCompleteness() {
  log("\n🔍 检查翻译完整性...", "blue");

  const enDir = path.join("./web/public/locales/en");
  const zhDir = path.join("./web/public/locales/zh");

  if (!fs.existsSync(enDir) || !fs.existsSync(zhDir)) {
    log("❌ 语言目录不完整", "red");
    return false;
  }

  const enFiles = fs
    .readdirSync(enDir)
    .filter((file) => file.endsWith(".json"));
  const zhFiles = fs
    .readdirSync(zhDir)
    .filter((file) => file.endsWith(".json"));

  log(`📁 英文文件数: ${enFiles.length}`, "cyan");
  log(`📁 中文文件数: ${zhFiles.length}`, "cyan");

  // 检查文件是否匹配
  const missingInZh = enFiles.filter((file) => !zhFiles.includes(file));
  const extraInZh = zhFiles.filter((file) => !enFiles.includes(file));

  if (missingInZh.length > 0) {
    log(`❌ 中文目录缺少文件: ${missingInZh.join(", ")}`, "red");
  }

  if (extraInZh.length > 0) {
    log(`⚠️  中文目录多余文件: ${extraInZh.join(", ")}`, "yellow");
  }

  // 检查每个文件的键完整性
  let totalEnKeys = 0;
  let totalZhKeys = 0;
  let missingKeys = [];

  enFiles.forEach((file) => {
    const enPath = path.join(enDir, file);
    const zhPath = path.join(zhDir, file);

    if (!fs.existsSync(zhPath)) {
      return;
    }

    try {
      const enContent = JSON.parse(fs.readFileSync(enPath, "utf8"));
      const zhContent = JSON.parse(fs.readFileSync(zhPath, "utf8"));

      const enKeys = Object.keys(enContent);
      const zhKeys = Object.keys(zhContent);

      totalEnKeys += enKeys.length;
      totalZhKeys += zhKeys.length;

      const missing = enKeys.filter((key) => !zhKeys.includes(key));
      const extra = zhKeys.filter((key) => !enKeys.includes(key));

      if (missing.length > 0 || extra.length > 0) {
        log(`\n📄 ${file}:`, "yellow");
        if (missing.length > 0) {
          log(`  ❌ 缺少键: ${missing.join(", ")}`, "red");
          missingKeys.push(...missing.map((key) => `${file}:${key}`));
        }
        if (extra.length > 0) {
          log(`  ➕ 多余键: ${extra.join(", ")}`, "yellow");
        }
      } else {
        log(`✅ ${file}: ${enKeys.length} 键完全匹配`, "green");
      }
    } catch (error) {
      log(`❌ 解析 ${file} 失败: ${error.message}`, "red");
    }
  });

  log(`\n📊 统计信息:`, "blue");
  log(`  英文总键数: ${totalEnKeys}`, "cyan");
  log(`  中文总键数: ${totalZhKeys}`, "cyan");
  log(
    `  缺少键数: ${missingKeys.length}`,
    missingKeys.length > 0 ? "red" : "green",
  );

  if (missingKeys.length === 0) {
    log("\n🎉 翻译完整性检查通过！", "green");
    return true;
  } else {
    log("\n❌ 翻译不完整，需要补充以下键:", "red");
    missingKeys.forEach((key) => log(`  - ${key}`, "red"));
    return false;
  }
}

// 检查特定组件的翻译键
function checkOnboardingTranslations() {
  log("\n🔍 检查引导页面翻译...", "blue");

  const requiredKeys = {
    datasets: [
      "getStartedWithDatasets",
      "datasetsDescription",
      "continuousImprovement",
      "continuousImprovementDesc",
      "preDeploymentTesting",
      "preDeploymentTestingDesc",
      "structuredTesting",
      "structuredTestingDesc",
      "customWorkflows",
      "customWorkflowsDesc",
      "createDataset",
      "learnMore",
    ],
    sessions: [
      "getStartedWithSessions",
      "sessionsDescription",
      "groupRelatedTraces",
      "groupRelatedTracesDesc",
      "trackUserInteractions",
      "trackUserInteractionsDesc",
      "analyzeConversationFlows",
      "analyzeConversationFlowsDesc",
      "sessionLevelMetrics",
      "sessionLevelMetricsDesc",
      "gettingStartedText",
      "documentation",
      "forMoreDetails",
    ],
  };

  let allKeysPresent = true;

  Object.entries(requiredKeys).forEach(([namespace, keys]) => {
    const zhPath = path.join(`./web/public/locales/zh/${namespace}.json`);

    if (!fs.existsSync(zhPath)) {
      log(`❌ 缺少翻译文件: ${namespace}.json`, "red");
      allKeysPresent = false;
      return;
    }

    try {
      const zhContent = JSON.parse(fs.readFileSync(zhPath, "utf8"));
      const missingKeys = keys.filter((key) => !zhContent.hasOwnProperty(key));

      if (missingKeys.length > 0) {
        log(`❌ ${namespace}.json 缺少键: ${missingKeys.join(", ")}`, "red");
        allKeysPresent = false;
      } else {
        log(`✅ ${namespace}.json: 所有必需键都存在`, "green");
      }
    } catch (error) {
      log(`❌ 解析 ${namespace}.json 失败: ${error.message}`, "red");
      allKeysPresent = false;
    }
  });

  return allKeysPresent;
}

// 主函数
function main() {
  log("🌐 Langfuse 翻译验证工具", "blue");
  log("================================", "blue");

  const completenessCheck = checkTranslationCompleteness();
  const onboardingCheck = checkOnboardingTranslations();

  log("\n📋 验证结果:", "blue");
  log("================================", "blue");

  if (completenessCheck && onboardingCheck) {
    log("🎉 所有翻译检查通过！", "green");
    log("✅ 翻译文件完整", "green");
    log("✅ 引导页面翻译完整", "green");
    process.exit(0);
  } else {
    log("❌ 翻译检查失败", "red");
    if (!completenessCheck) {
      log("❌ 翻译文件不完整", "red");
    }
    if (!onboardingCheck) {
      log("❌ 引导页面翻译不完整", "red");
    }
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkTranslationCompleteness,
  checkOnboardingTranslations,
};
