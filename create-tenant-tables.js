const { Client } = require("pg");

async function createTenantTables() {
  const client = new Client({
    host: "localhost",
    port: 5432,
    user: "postgres",
    password: "postgres",
    database: "postgres",
  });

  try {
    await client.connect();
    console.log("Connected to PostgreSQL");

    // Create enums
    await client.query(`
      DO $$ BEGIN
          CREATE TYPE "TenantType" AS ENUM ('enterprise', 'standard', 'basic');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
          CREATE TYPE "TenantStatus" AS ENUM ('active', 'inactive', 'suspended', 'pending');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);

    await client.query(`
      DO $$ BEGIN
          CREATE TYPE "TenantRole" AS ENUM ('owner', 'admin', 'member', 'viewer');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);

    console.log("Enums created successfully");

    // Create tenants table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "tenants" (
          "id" TEXT NOT NULL,
          "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "name" TEXT NOT NULL,
          "display_name" TEXT,
          "description" TEXT,
          "type" "TenantType" NOT NULL DEFAULT 'standard',
          "category" TEXT,
          "contact_name" TEXT,
          "contact_email" TEXT,
          "contact_phone" TEXT,
          "address" TEXT,
          "website" TEXT,
          "license_number" TEXT,
          "tax_id" TEXT,
          "legal_person" TEXT,
          "status" "TenantStatus" NOT NULL DEFAULT 'pending',
          "is_active" BOOLEAN NOT NULL DEFAULT true,
          "is_verified" BOOLEAN NOT NULL DEFAULT false,
          "verified_at" TIMESTAMP(3),
          "suspended_at" TIMESTAMP(3),
          "settings" JSONB,
          "metadata" JSONB,
          "max_users" INTEGER,
          "max_projects" INTEGER,
          "max_applications" INTEGER,
          "storage_limit" BIGINT,
          CONSTRAINT "tenants_pkey" PRIMARY KEY ("id")
      );
    `);

    console.log("Tenants table created successfully");

    // Create tenant_organizations table
    await client.query(`
      CREATE TABLE IF NOT EXISTS "tenant_organizations" (
          "id" TEXT NOT NULL,
          "tenant_id" TEXT NOT NULL,
          "org_id" TEXT NOT NULL,
          "role" "TenantRole" NOT NULL DEFAULT 'member',
          "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT "tenant_organizations_pkey" PRIMARY KEY ("id")
      );
    `);

    console.log("Tenant organizations table created successfully");

    // Create indexes
    await client.query(
      `CREATE INDEX IF NOT EXISTS "tenants_name_idx" ON "tenants"("name");`,
    );
    await client.query(
      `CREATE INDEX IF NOT EXISTS "tenants_status_idx" ON "tenants"("status");`,
    );
    await client.query(
      `CREATE INDEX IF NOT EXISTS "tenants_type_idx" ON "tenants"("type");`,
    );
    await client.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "tenant_organizations_tenant_id_org_id_key" ON "tenant_organizations"("tenant_id", "org_id");`,
    );
    await client.query(
      `CREATE INDEX IF NOT EXISTS "tenant_organizations_org_id_idx" ON "tenant_organizations"("org_id");`,
    );
    await client.query(
      `CREATE INDEX IF NOT EXISTS "tenant_organizations_tenant_id_idx" ON "tenant_organizations"("tenant_id");`,
    );

    console.log("Indexes created successfully");

    console.log("All tenant tables and indexes created successfully!");
  } catch (error) {
    console.error("Error creating tenant tables:", error);
  } finally {
    await client.end();
  }
}

createTenantTables();
