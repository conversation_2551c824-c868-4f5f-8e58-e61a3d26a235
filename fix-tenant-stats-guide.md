# 租户管理统计数据修复指南

## 问题诊断

租户管理的总览统计数据显示为0，主要原因是：

1. **数据库中没有租户数据**
2. **tenant_organizations关联表为空**
3. **查询逻辑过于复杂，依赖关联表**

## 修复方案

### 1. 代码修复（已完成）

修改了 `web/src/features/tenant-management/server/tenantRouter.ts` 中的统计查询逻辑：

- 添加了调试日志输出
- 检查关联表数据是否存在
- 如果没有关联数据，使用简化统计（直接统计所有租户）
- 保留原有的复杂关联查询作为备选

### 2. 创建测试数据

执行以下步骤创建测试租户数据：

#### 步骤1：执行SQL脚本

```bash
# 连接到数据库并执行测试数据脚本
psql -d your_database_name -f create-test-tenant-data.sql
```

或者在数据库管理工具中执行 `create-test-tenant-data.sql` 文件中的SQL语句。

#### 步骤2：验证数据

执行以下查询验证数据是否插入成功：

```sql
-- 检查租户数据
SELECT 
    status,
    COUNT(*) as count
FROM tenants 
GROUP BY status
ORDER BY status;

-- 应该看到类似结果：
-- status    | count
-- ----------+-------
-- active    |   5
-- pending   |   3
-- suspended |   1
```

### 3. 测试统计功能

1. 重启开发服务器
2. 访问租户管理页面
3. 查看控制台日志，应该能看到调试输出
4. 统计卡片应该显示正确的数据

### 4. 预期结果

修复后，统计卡片应该显示：

- **总租户数**: 10
- **活跃租户**: 5 (活跃率 50%)
- **待审核**: 3
- **已暂停**: 1

## 调试信息

如果问题仍然存在，请检查浏览器控制台和服务器日志中的调试输出：

```
获取租户统计 - projectId: xxx
项目组织ID: xxx
总租户数: 10
租户组织关联数: 0
没有租户组织关联数据，使用简化统计
简化统计结果: { totalCount: 10, activeCount: 5, pendingCount: 3, suspendedCount: 1 }
```

## 后续优化

1. **完善关联逻辑**：如果需要按组织过滤租户，需要创建 tenant_organizations 关联数据
2. **权限控制**：根据用户权限显示相应的租户数据
3. **移除调试日志**：功能正常后可以移除调试输出

## 创建组织关联（可选）

如果需要按组织过滤租户，可以执行以下步骤：

1. 查询现有组织ID：
```sql
SELECT id, name FROM organizations LIMIT 5;
```

2. 为租户创建组织关联：
```sql
INSERT INTO tenant_organizations (
    id,
    tenant_id,
    org_id,
    role
) VALUES 
('to_001', 'tenant_001', 'your_org_id', 'member'),
('to_002', 'tenant_002', 'your_org_id', 'member')
-- ... 更多关联
ON CONFLICT (id) DO NOTHING;
```

## 测试数据说明

创建的测试数据包括：

- **3个三甲医院**（2个活跃，1个暂停）
- **2个二甲医院**（1个活跃，1个待审核）
- **1个专科医院**（待审核）
- **1个诊所**（活跃）
- **1个卫生院**（活跃）
- **1个医疗集团**（活跃）
- **1个互联网医院**（待审核）

这些数据涵盖了不同的租户类型和状态，便于测试各种统计功能。
