{"name": "langfuse", "version": "3.103.0", "author": "<EMAIL>", "license": "MIT", "private": true, "engines": {"node": "24"}, "scripts": {"preinstall": "npx only-allow pnpm", "infra:dev:up": "docker compose -f ./docker-compose.dev.yml up -d --wait", "infra:dev:down": "docker compose -f ./docker-compose.dev.yml down", "infra:dev:prune": "docker compose -f ./docker-compose.dev.yml down -v", "db:generate": "turbo run db:generate", "db:migrate": "turbo run db:migrate", "db:seed": "turbo run db:seed", "db:seed:examples": "turbo run db:seed:examples", "nuke": "bash ./scripts/nuke.sh", "dx": "pnpm i && pnpm run infra:dev:prune && pnpm run infra:dev:up --pull always && pnpm --filter=shared run db:reset:test && pnpm --filter=shared run db:reset && pnpm --filter=shared run ch:reset && pnpm --filter=shared run db:seed:examples && pnpm run dev", "dx-f": "pnpm i && pnpm run infra:dev:prune && pnpm run infra:dev:up --pull always && pnpm --filter=shared run db:reset:test && pnpm --filter=shared run db:reset -f && SKIP_CONFIRM=1 pnpm --filter=shared run ch:reset && pnpm --filter=shared run db:seed:examples && pnpm run dev", "dx:skip-infra": "pnpm i && pnpm --filter=shared run db:reset:test && pnpm --filter=shared run db:reset && pnpm --filter=shared run ch:reset && pnpm --filter=shared run db:seed:examples && pnpm run dev", "build": "turbo run build", "start": "turbo run start", "dev": "turbo run dev", "dev:worker": "turbo run dev --filter=worker", "dev:web": "turbo run dev --filter=web", "dev:web-turbo": "turbo run dev --filter=web -- --turbo", "lint": "turbo run lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\" --experimental-cli", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,css}\" --experimental-cli", "test": "turbo run test", "release": "dotenv -e ../.env -- release-it", "prepare": "husky"}, "devDependencies": {"@release-it/bumper": "^7.0.5", "braces": "3.0.3", "dotenv-cli": "^7.4.2", "husky": "^9.0.11", "prettier": "^3.6.2", "release-it": "^19.0.3", "turbo": "^2.5.6"}, "release-it": {"git": {"commitMessage": "chore: release v${version}", "tagName": "v${version}", "commitArgs": ["--no-verify"], "pushArgs": ["--no-verify"]}, "plugins": {"@release-it/bumper": {"out": [{"file": "./web/src/constants/VERSION.ts", "type": "application/typescript"}, {"file": "./worker/src/constants/VERSION.ts", "type": "application/typescript"}, {"file": "./web/package.json"}, {"file": "./worker/package.json"}]}}, "github": {"release": true, "web": true, "autoGenerate": true, "releaseName": "v${version}", "comments": {"submit": true, "issue": ":rocket: _This issue has been resolved in v${version}. See [${releaseName}](${releaseUrl}) for release notes._", "pr": ":rocket: _This pull request is included in v${version}. See [${releaseName}](${releaseUrl}) for release notes._"}}}, "packageManager": "pnpm@9.5.0", "pnpm": {"overrides": {"nanoid": "^3.3.8", "katex": "^0.16.21", "tar-fs": "^2.1.2", "rollup@^4.0.0": "^4.22.4", "@types/node-fetch": "^2.6.13"}, "patchedDependencies": {"next-auth@4.24.11": "patches/<EMAIL>"}}, "dependencies": {"pg": "^8.13.0"}}